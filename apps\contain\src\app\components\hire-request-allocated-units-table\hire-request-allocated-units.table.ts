import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { HireRequestUnitTableFields } from 'apps/contain/src/app/shared/enums/hire-request-unit-table-fields.enum';
import { TableModule } from 'primeng/table';
import { HireRequest } from 'libs/services/src/lib/services/contain/interfaces/hire-request.interface';
import { HireRequestAllocatedUnitTableFields } from '../../shared/enums/hire-request-allocated-unit-table-fields.enum';
import { Store } from '@ngrx/store';
import { cargoesFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { ReactiveFormsModule } from '@angular/forms';
import { HireRequestCargoCreate } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo-create.interface';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { HireRequestCargo } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo.interface';
import { CreateCargoHireDialog } from "../create-cargo-hire-dialog/create-cargo-hire.dialog";
import { CargoHireDetailsDialogComponent } from "../cargo-hire-details-dialog/cargo-hire-details.dialog";

@Component({
  selector: 'contain-hire-request-allocated-units-table',
  standalone: true,
  imports: [
    NgForOf,
    NgIf,
    DialogModule,
    TableModule,
    ReactiveFormsModule,
    DropdownModule,
    ButtonModule,
    CreateCargoHireDialog,
    CargoHireDetailsDialogComponent
  ],
  templateUrl: './hire-request-allocated-units.table.html',
})
export class HireRequestAllocatedUnitsTable implements OnInit {
  @Output() allocatedUnitsModified = new EventEmitter<boolean>();
  @Input() hireRequest!: HireRequest;
  store = inject(Store);

  cargoes = this.store.selectSignal(cargoesFeature.selectCargoesIncludingAdhoc);
  hireRequestCargoes = this.store.selectSignal(
    hireRequestFeature.selectHireRequestCargoes
  );
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);

  hireRequestCargoForm: FormGroup = new FormGroup({});

  listColumns = [
    new ColumnModel(HireRequestAllocatedUnitTableFields.cargoCcuId, 'Unit', 60),
    new ColumnModel(
      HireRequestAllocatedUnitTableFields.ccuOwner,
      'CCU Owner',
      60
    ),
    new ColumnModel(HireRequestUnitTableFields.action, '', 40),
  ] as ColumnModel[];
  tableWidth = 3235;
  confirmHireDialogVisible = false;
  hireRequestCargo: HireRequestCargo | null = null;
  detailsDialogVisible = false;

  ngOnInit(): void {
    this.store.dispatch(
      HireRequestActions.load_Hire_Request_Cargoes_By_Hire_Request_Id({
        hireRequestId: this.hireRequest.hireRequestId,
      })
    );

    this.hireRequestCargoForm = this.initialiseForm();
  }
  initialiseForm(): FormGroup {
    return new FormGroup({
      cargoId: new FormControl<string>('', Validators.required),
      hireRequestId: new FormControl<string>(
        this.hireRequest.hireRequestId,
        Validators.required
      ),
      clientId: new FormControl<string>(
        this.hireRequest.clientId,
        Validators.required
      ),
      vendorId: new FormControl<string>(
        this.hireRequest.vendorId!,
        Validators.required
      ),
      assetId: new FormControl<string>(
        this.hireRequest.assetId,
        Validators.required
      ),
    });
  }

  removeHireRequestCargo(hireRequestCargoId: string) {
    this.store.dispatch(
      HireRequestActions.remove_Hire_Request_Cargo({ hireRequestCargoId, filterModel: this.filterModel() })
    );

    //emit event to parent component that units have been modified
    this.allocatedUnitsModified.emit(true);
  }

  createHire(hireRequestCargo: HireRequestCargo): void {
    this.hireRequestCargo = hireRequestCargo;
    this.confirmHireDialogVisible = true;
  }

  cargoHireDetails(hireRequestCargo: HireRequestCargo): void {
    this.hireRequestCargo = hireRequestCargo;

    this.store.dispatch(
      HireRequestActions.load_Cargo_Hire_By_Id({
        hireRequestCargoId: this.hireRequestCargo?.hireRequestCargoId ?? '',
      })
    );

    this.detailsDialogVisible = true;
  }

  hideConfirmHireDialog() {
    this.confirmHireDialogVisible = false;
  }

  setDetailsDialogVisible(): void {
    this.detailsDialogVisible = !this.detailsDialogVisible;
  }

  submit(): void {
    this.hireRequestCargoForm.markAllAsTouched();

    if (this.hireRequestCargoForm.valid) {
      const model: HireRequestCargoCreate = {
        cargoId: this.hireRequestCargoForm.value.cargoId!,
        hireRequestId: this.hireRequestCargoForm.value.hireRequestId!,
        clientId: this.hireRequestCargoForm.value.clientId!,
        assetId: this.hireRequestCargoForm.value.assetId!,
        vendorId: this.hireRequestCargoForm.value.vendorId!,
      };

      //emit event to parent component that units have been modified
      this.allocatedUnitsModified.emit(true);

      this.store.dispatch(
        HireRequestActions.create_Hire_Request_Cargo({
          hireRequestCargo: model,
        })
      );
      this.hireRequestCargoForm = this.initialiseForm();
    }
  }
}
