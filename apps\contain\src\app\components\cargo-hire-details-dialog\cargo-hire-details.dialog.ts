import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  signal,
} from '@angular/core';
import { NgForOf, NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { Actions } from '@ngrx/effects';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { DatePipe } from '@angular/common';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestCargo } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo.interface';
import { UpdateCargoHireDialog } from '../update-cargo-hire-dialog/update-cargo-hire.dialog';
import { EditCargoHireDialog } from '../edit-cargo-hire-dialog/edit-cargo-hire.dialog';
import { CargoHireNoteDialog } from '../cargo-hire-note-dialog/cargo-hire-note.dialog';
import { ColumnModel } from 'libs/components/src/lib/models/column.model';
import { HireRequestCargoEventTableFields } from '../../shared/enums/cargo-hire-events-table-fields.enum';
import { TableModule } from 'primeng/table';
import { HireRequestCargoEventType } from 'libs/services/src/lib/services/contain/interfaces/enums/hire-request-cargo-event-type.enum';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { HireRequestCargoEvent } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo-event.interface';
import { ConfirmationService } from 'primeng/api';

const eventTypeDescriptions: { [key: number]: string } = {
  [HireRequestCargoEventType.OnHired]: 'On Hired',
  [HireRequestCargoEventType.OffHired]: 'Off Hired',
  [HireRequestCargoEventType.Shipped]: 'Shipped',
  [HireRequestCargoEventType.Returned]: 'Returned',
  [HireRequestCargoEventType.RoundTrip]: 'Round Trip',
  [HireRequestCargoEventType.RemainOnBoard]: 'Remain On Board',
  [HireRequestCargoEventType.HireNote]: 'Hire Note',
};

@Component({
  selector: 'contain-cargo-hire-details-dialog',
  standalone: true,
  imports: [
    NgForOf,
    NgIf,
    DialogModule,
    DividerModule,
    FormsModule,
    InputTextModule,
    DatePipe,
    UpdateCargoHireDialog,
    EditCargoHireDialog,
    CargoHireNoteDialog,
    TableModule,
  ],
  templateUrl: './cargo-hire-details.dialog.html',
  styleUrls: ['./cargo-hire-details.dialog.scss'],
})
export class CargoHireDetailsDialogComponent implements OnInit {
  @Input() dialogVisible: boolean = false;
  @Output() dialogToggle = new EventEmitter<void>();
  private readonly confirmationService = inject(ConfirmationService);

  store = inject(Store);
  action = inject(Actions);
  listColumns = signal<ColumnModel[]>([]);
  tableWidth = 3235;
  eventType = HireRequestCargoEventType;
  eventTypeDescriptions = eventTypeDescriptions;
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);
  cargoHireEvents = this.store.selectSignal(
    hireRequestFeature.selectCargoHireEvents
  );
  hireRequestCargo = this.store.selectSignal(hireRequestFeature.selectCargoHire);

  updateDialogVisible = false;
  editDialogVisible = false;
  noteDialogVisible = false;

  ngOnInit(): void {
    this.listColumns.set([
      new ColumnModel(HireRequestCargoEventTableFields.eventType, 'Event', 60),
      new ColumnModel(
        HireRequestCargoEventTableFields.eventDate,
        'Event Date',
        60
      ),
      new ColumnModel(HireRequestCargoEventTableFields.details, 'Details', 60),
      new ColumnModel(HireRequestCargoEventTableFields.action, '', 30),
    ]);
  }

  hideDialog() {
    this.dialogToggle.emit();
  }

  toggleUpdateDialog() {
    this.updateDialogVisible = !this.updateDialogVisible;
  }

  updateCargoHire() {
    this.updateDialogVisible = true;
  }

  editCargoHire() {
    this.editDialogVisible = true;
    this.store.dispatch(
      HireRequestActions.load_Cargo_Hire_By_Id({
        hireRequestCargoId: this.hireRequestCargo()?.hireRequestCargoId!,
      })
    );
  }

  hideEditDialog(isDeleteDialog: boolean) {
    this.editDialogVisible = false;

    if (isDeleteDialog) {
      this.hideDialog();
    }
  }

  hideNoteDialog() {
    this.noteDialogVisible = false;
  }

  createNoteDialog() {
    this.noteDialogVisible = true;
  }

  deleteNote(hireRequestCargoEvent: HireRequestCargoEvent): void {
    this.store.dispatch(
      HireRequestActions.remove_Cargo_Hire_Note({
        hireRequestCargoEventId: hireRequestCargoEvent.hireRequestCargoEventId,
      })
    );
  }

  onDelete(event: Event, hireRequestCargoEvent: HireRequestCargoEvent) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to delete this Hire Note?',
      header: 'Delete',
      rejectButtonStyleClass: 'btn-tertiary',
      acceptButtonStyleClass: 'btn-negative-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      accept: () => {
        this.deleteNote(hireRequestCargoEvent);
      },
    });
  }
}
