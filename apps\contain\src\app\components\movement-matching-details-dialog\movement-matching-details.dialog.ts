import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { NgIf } from '@angular/common';
import { Store } from '@ngrx/store';
import { Actions } from '@ngrx/effects';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { DatePipe } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { MovementMatching } from 'libs/services/src/lib/services/contain/interfaces/movement-matching/movement-matching.interface';
import { VoyageDirection } from 'libs/services/src/lib/services/voyages/enums/voyage-direction.enum';
import { CargoHireDetailsDialogComponent } from '../cargo-hire-details-dialog/cargo-hire-details.dialog';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';

@Component({
  selector: 'contain-movement-matching-details-dialog',
  standalone: true,
  imports: [
    NgIf,
    DialogModule,
    DividerModule,
    InputTextModule,
    DatePipe,
    ButtonModule,
    CargoHireDetailsDialogComponent,
  ],
  templateUrl: './movement-matching-details.dialog.html',
  styleUrls: ['./movement-matching-details.dialog.scss'],
})
export class MovementMatchingDetailsDialogComponent implements OnInit {
  @Input() dialogVisible: boolean = false;
  @Input() movementMatching!: MovementMatching;
  @Output() dialogToggle = new EventEmitter<void>();

  movementDirection = VoyageDirection;
  store = inject(Store);
  action = inject(Actions);
  hireDetailsDialogVisible = false;

  hireRequestCargo = this.store.selectSignal(
    hireRequestFeature.selectCargoHire
  );

  ngOnInit(): void {
    if (this.movementMatching?.hireRequestCargoId && this.movementMatching?.hireRequestCargoId != '00000000-0000-0000-0000-000000000000') {
      this.store.dispatch(
        HireRequestActions.load_Cargo_Hire_By_Id({
          hireRequestCargoId: this.movementMatching.hireRequestCargoId,
        })
      );
    } else {
      this.store.dispatch(
        HireRequestActions.clear_store_hire_request_cargo()
      );
    }
  }

  hideDialog() {
    this.dialogToggle.emit();
  }

  viewHire(): void {
    if (!this.movementMatching?.hireRequestCargoId || this.movementMatching?.hireRequestCargoId == '00000000-0000-0000-0000-000000000000') {
      return;
    }
    this.store.dispatch(
      HireRequestActions.load_Cargo_Hire_By_Id({
        hireRequestCargoId: this.movementMatching.hireRequestCargoId,
      })
    );
    this.hireDetailsDialogVisible = !this.hireDetailsDialogVisible;
  }

  viewVoyage() {
    window.open(`/flow/voyage/${this.movementMatching.voyageId}`);
  }
}
