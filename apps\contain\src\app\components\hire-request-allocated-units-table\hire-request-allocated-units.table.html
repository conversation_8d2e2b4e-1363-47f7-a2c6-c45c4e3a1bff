<form *ngIf="hireRequest.unitQuantity > hireRequestCargoes().length" [formGroup]="hireRequestCargoForm">
    <div class="d-flex flex-wrap gap-16">
        <div class="flex-column" style="flex: 1;">
            <p-dropdown
                        [options]="cargoes()"
                        [filter]="true"
                        styleClass="new-version"
                        optionLabel="ccuId"
                        formControlName="cargoId"
                        class="cargo_dropdown"
                        optionValue="cargoId"
                        inputId="cargo"
                        placeholder="Select Cargo"
                        [showClear]="true"
                        appendTo="body" />
        </div>
        <div class="flex-column" style="flex: 1;">
            <button
                    class="btn-primary"
                    type="button"
                    [disabled]="!hireRequestCargoForm.valid"
                    (click)="submit()">
                Add
            </button>
        </div>
    </div>
</form>

<p-table class="pb-20 pt-20" [columns]="listColumns" [value]="hireRequestCargoes()" [scrollable]="true">
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th *ngFor="let column of columns" scope="col" [style.min-width.px]="column.width"
                [style.width.%]="(column.width / tableWidth) * 100">
                <span>{{ column.name }}</span>
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-hireRequestCargo>
        <tr>
            <td>{{hireRequestCargo.cargoCCUId}}</td>
            <td>{{hireRequestCargo.vendorVendorName}}</td>
            <td *ngIf="!hireRequestCargo.onHiredDate; else hireDetails">
                <button
                        class="btn-primary"
                        type="button"
                        (click)="createHire(hireRequestCargo)">
                    Create Hire
                </button>
                <button
                        class="btn-negative-primary ml-8 "
                        type="button"
                        (click)="removeHireRequestCargo(hireRequestCargo.hireRequestCargoId)">
                    Remove
                </button>
            </td>
            <ng-template #hireDetails>
                <td>
                    <button
                            class="btn-primary "
                            type="button"
                            (click)="cargoHireDetails(hireRequestCargo)">
                        Hire Details
                    </button>
                </td>
            </ng-template>
        </tr>
    </ng-template>
</p-table>

<contain-create-cargo-hire-dialog
*ngIf="confirmHireDialogVisible && hireRequestCargo?.hireRequestCargoId"
[dialogVisible]="confirmHireDialogVisible"
[hireRequestCargo]="hireRequestCargo!"
[isCcuList]="false"
[cargo]="hireRequestCargo?.cargo"
(dialogToggle)="hideConfirmHireDialog()">
</contain-create-cargo-hire-dialog>

<contain-cargo-hire-details-dialog
*ngIf="detailsDialogVisible && hireRequestCargo?.hireRequestCargoId"
[dialogVisible]="detailsDialogVisible"
(dialogToggle)="setDetailsDialogVisible()">
</contain-cargo-hire-details-dialog>
