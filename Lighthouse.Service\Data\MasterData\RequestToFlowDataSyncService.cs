
namespace Lighthouse.Service.Data.MasterData
{
    public class RequestToFlowDataSyncService : IRequestToFlowDataSyncService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IVoyageCargoService _voyageCargoService;
        private readonly IUserService _userService;

        public RequestToFlowDataSyncService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IVoyageCargoService voyageCargoService,
            IUserService userService
            )
        {
            _voyageCargoService = voyageCargoService;
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
        }

        public bool CheckAutoApproval(TransportRequest tr, DateTime submissionTime)
        {
            var earliestActivityStartTime = tr.SailingRequest.SailingRequestActivities?
            .Where(x => x.IsInPort && x.StartTime.HasValue)
            .OrderBy(x => x.StartTime)
            .FirstOrDefault()?
            .StartTime;

            if (!earliestActivityStartTime.HasValue)
            {
                return true;
            }

            if (earliestActivityStartTime.HasValue && tr.VoyageDirection != VoyageDirection.Interfield)
            {
                if (tr.VoyageDirection == VoyageDirection.Outbound)
                {
                    // Auto-approve if submitted after 14:00 Qatar time (11:00 UTC) the day BEFORE loading
                    var outboundApprovalDeadline = earliestActivityStartTime.Value.AddDays(-1).Date.AddHours(11);
                    return submissionTime >= outboundApprovalDeadline;
                }
                else if (tr.VoyageDirection == VoyageDirection.Inbound)
                {
                    // Auto-approve if submitted after 14:00 Qatar time (11:00 UTC) the day OF loading (hardcoded)
                    var inboundApprovalDeadline = earliestActivityStartTime.Value.Date.AddHours(11);
                    return submissionTime >= inboundApprovalDeadline;
                }
            }
            return false;
        }

        public async Task<TransportRequest> GetTrDirectionByTrIdAsync(Guid transportRequestId)
        {
            return await _unitOfWork.Repository<TransportRequest>()
                .Query(x => x.TransportRequestId == transportRequestId)
                .AsNoTracking()
                .Include(x => x.SailingRequest)
                    .ThenInclude(x => x.SailingRequestActivities)
                .FirstOrDefaultAsync();
        }

        public async Task<int> GetTrItemsSnapshotVersionsByTrIdAsync(Guid transportRequestId)
        {
            return await _unitOfWork.Repository<TransportRequestCargoSnapshot>()
                .Query(x => x.TransportRequestId == transportRequestId).AsNoTracking().CountAsync() + 1;
        }

        public async Task UpdateTransportRequestItemStatus(Guid transportRequestId, Guid submittedById, bool autoApprove, DateTime submissionTime)
        {
            var transportRequestCargoesUpdated = await UpdateTransportRequestCargoStatus(transportRequestId, submittedById, autoApprove, submissionTime);

            await _unitOfWork.Repository<TransportRequestBulkCargo>()
                .Query(x => x.TransportRequestId == transportRequestId)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.Status, TransportRequestCargoStatus.Submitted)
                    .SetProperty(p => p.SubmittedById, submittedById)
                    .SetProperty(p => p.SubmittedDate, submissionTime)
                );

            await UpdateMaterailDetailStatusBasedOnUpdatedCargoes(transportRequestCargoesUpdated, transportRequestId, submittedById, submissionTime);
        }

        private async Task UpdateMaterailDetailStatusBasedOnUpdatedCargoes(
            List<TransportRequestCargo> transportRequestCargoesUpdated,
            Guid transportRequestId,
            Guid submittedById,
            DateTime submissionTime
            )
        {
            var transportRequestMaterialDetailsToUpdate = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query(x => x.TransportRequestId == transportRequestId).ToListAsync();

            RemovePendingOrDraftCargoes(transportRequestCargoesUpdated, transportRequestMaterialDetailsToUpdate);

            var materialDetailsToUpdate = new List<TransportRequestMaterialDetail>();

            foreach (var materialDetailToUpdate in transportRequestMaterialDetailsToUpdate)
            {
                materialDetailToUpdate.Status = TransportRequestCargoStatus.Submitted;
                materialDetailToUpdate.SubmittedById = submittedById;
                materialDetailToUpdate.SubmittedDate = submissionTime;

                materialDetailsToUpdate.Add(materialDetailToUpdate);
            }
        }

        private void RemovePendingOrDraftCargoes(
            List<TransportRequestCargo> transportRequestCargoesUpdated,
            List<TransportRequestMaterialDetail> transportRequestMaterialDetailsToUpdate
            )
        {
            var pendingOrDraftCargoes = transportRequestCargoesUpdated
               .Where(x => x.Status == TransportRequestCargoStatus.Pending || x.Status == TransportRequestCargoStatus.Draft);

            foreach (var pendingOrDraftCargo in pendingOrDraftCargoes)
            {
                var materialDetailToRemove = transportRequestMaterialDetailsToUpdate
                    .Where(x => x.TransportRequestCargoId == pendingOrDraftCargo.TransportRequestCargoId)
                    .FirstOrDefault();

                if (materialDetailToRemove is not null)
                {
                    materialDetailToRemove.Status = TransportRequestCargoStatus.Draft;
                }

                transportRequestMaterialDetailsToUpdate.Remove(materialDetailToRemove);
            }
        }

        private async Task<List<TransportRequestCargo>> UpdateTransportRequestCargoStatus(Guid transportRequestId, Guid submittedById, bool autoApprove, DateTime submissionTime)
        {
            var transportRequestCargoesToUpdate = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query(x => x.TransportRequestId == transportRequestId)
                .ToListAsync();

            foreach (var cargoToUpdate in transportRequestCargoesToUpdate)
            {
                if (autoApprove)
                {
                    cargoToUpdate.Status = TransportRequestCargoStatus.Approved;
                    cargoToUpdate.AutoApprove = true;
                }
                else
                {
                    cargoToUpdate.Status = TransportRequestCargoStatus.Pending;
                    cargoToUpdate.AutoApprove = false;
                }
                cargoToUpdate.SubmittedById = submittedById;
                cargoToUpdate.SubmittedDate = submissionTime;
            }
            await _unitOfWork.SaveChangesAsync();

            return transportRequestCargoesToUpdate;
        }

        public async Task CreateTransportRequestCargoSnapshot(
            (List<TransportRequestCargo> trcs, List<TransportRequestBulkCargo> trbcs, List<TransportRequestMaterialDetail> trmds) trItems,
            int version,
            Guid transportRequestId,
            Guid userId,
            DateTime dateTimeNow
            )
        {
            Guid snapshotId = Guid.NewGuid();
            var mappedTrcs = JsonSerializer.Serialize(_mapper.Map<List<TransportRequestCargoModel>>(trItems.trcs));
            var mappedTrbs = JsonSerializer.Serialize(_mapper.Map<List<TransportRequestBulkCargoModel>>(trItems.trbcs));
            var mappedTrmds = JsonSerializer.Serialize(_mapper.Map<List<TransportRequestMaterialDetailModel>>(trItems.trmds));


            var trcSnapshot = new TransportRequestCargoSnapshot
            {
                TransportRequestCargoSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = userId,
                Version = $"V{version}",
                Content = mappedTrcs,
                TransportRequestId = transportRequestId,
            };

            var trbcSnapshot = new TransportRequestBulkCargoSnapshot
            {
                TransportRequestBulkCargoSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = userId,
                Version = $"V{version}",
                Content = mappedTrbs,
                TransportRequestId = transportRequestId,
            };

            var trMdSnapshot = new TransportRequestMaterialDetailSnapshot
            {
                TransportRequestMaterialDetailSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = userId,
                Version = $"V{version}",
                Content = mappedTrmds,
                TransportRequestId = transportRequestId,
            };

            try
            {
                await _unitOfWork.Repository<TransportRequestCargoSnapshot>().CreateAsync(trcSnapshot);
                await _unitOfWork.Repository<TransportRequestBulkCargoSnapshot>().CreateAsync(trbcSnapshot);
                await _unitOfWork.Repository<TransportRequestMaterialDetailSnapshot>().CreateAsync(trMdSnapshot);
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackAsync();
                throw new Exception("Error creating Transport Request Snapshots", ex);
            }
        }

        public async Task DataSyncBetweenRequestAndFlow(
            Guid? doesTRVoyageExistId,
            DateTime dateTimeNow,
            (
                List<TransportRequestCargo> trcs,
                List<TransportRequestBulkCargo> trbcs,
                List<TransportRequestMaterialDetail> trmds
            ) trItems,
            UserModel user,
            bool bypassApprovedCargoesCheck
            )
        {
            if (doesTRVoyageExistId != null)
            {
                var updatedVoyageItems = await MapVoyageCargoesByTRId(doesTRVoyageExistId, dateTimeNow, trItems, user.UserId, bypassApprovedCargoesCheck);

                var dataForSnapshot = (
                    updatedVoyageItems.updatedVoyageCargoes,
                    updatedVoyageItems.updatedVoyageBulkCargoes,
                    updatedVoyageItems.updatedVoyageMaterialDetails
                );

                await CreateVoyageCargoSnapshot(dataForSnapshot, doesTRVoyageExistId, user.UserId, dateTimeNow);

                var dataForCreateEdit = (
                    updatedVoyageItems.vC,
                    updatedVoyageItems.vCb,
                    updatedVoyageItems.vMd,
                    updatedVoyageItems.vCDg,
                    updatedVoyageItems.vBCDg
                );
                await UpdateCargoesInVoyageByTheCargoesFromRequest(dataForCreateEdit, doesTRVoyageExistId, dateTimeNow, user.UserId);
            }
        }

        public async Task<(List<TransportRequestCargo> trcs, List<TransportRequestBulkCargo> trbcs, List<TransportRequestMaterialDetail> trmds)>
            GetTrItemsByTransportRequestIdAsync(Guid transportRequestId)
        {
            var trcs = new List<TransportRequestCargo>();
            var trbcs = new List<TransportRequestBulkCargo>();
            var trmds = new List<TransportRequestMaterialDetail>();
            try
            {
                trcs = await _unitOfWork.Repository<TransportRequestCargo>()
                   .Query()
                   .AsNoTracking()
                   .AsSplitQuery()
                   .Include(x => x.TransportRequest).ThenInclude(tr => tr.SailingRequest).ThenInclude(v => v.SailingRequestActivities).ThenInclude(x => x.ActivityCategoryType)
                   .Include(x => x.TransportRequest).ThenInclude(tr => tr.SailingRequest).ThenInclude(v => v.InboundVoyage).ThenInclude(x => x.VoyagePlanningDetails)
                   .Include(x => x.TransportRequest).ThenInclude(tr => tr.Voyage).ThenInclude(x => x.VoyagePlanningDetails)
                   .Include(x => x.TransportRequest).ThenInclude(tr => tr.SailingRequest).ThenInclude(v => v.Client)
                   .Include(x => x.Vendor).ThenInclude(x => x.VendorWarehouses)
                   .Include(x => x.Cargo).ThenInclude(x => x.Vendor)
                   .Include(x => x.TransportRequestCargoBundling)
                   .Include(x => x.FromAsset)
                   .Include(x => x.ToAsset)
                   .Include(x => x.FromLocation)
                   .Include(x => x.ToLocation)
                   .Include(x => x.ViaVendor)
                   .Include(x => x.VendorWarehouse)
                   .Include(x => x.ViaVendorWarehouse)
                   .Include(x => x.TransportRequestCargoDangerousGoods)
                   .Include(x => x.TransportRequestCargoAttachments)
                   .Include(x => x.SubmittedBy)
                   .Include(x => x.UpdatedBy)
                   .OrderBy(s => s.CreatedDate)
                   .Where(x => x.TransportRequestId == transportRequestId)
                   .ToListAsync();

                var cargoesThatCannotBeMoved = await _voyageCargoService.GetTransportRequestCargoesThatCannotBeCancelledByVoyageId(transportRequestId);

                if (cargoesThatCannotBeMoved.Count > 0 && cargoesThatCannotBeMoved is not null)
                {
                    trcs = trcs.Where(x => !cargoesThatCannotBeMoved.Contains(x.TransportRequestCargoId)).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error getting Transport Request Cargo items", ex);
            }

            try
            {
                trbcs = await _unitOfWork.Repository<TransportRequestBulkCargo>()
                    .Query()
                    .AsNoTracking()
                    .AsSplitQuery()
                    .Include(x => x.TransportRequest)
                    .Include(x => x.TransportRequest).ThenInclude(x => x.Voyage)
                    .Include(x => x.BulkType)
                    .Include(x => x.TransportRequestBulkCargoDangerousGood)
                    .Include(x => x.Vendor)
                    .Include(x => x.FromAsset)
                    .Include(x => x.FromLocation)
                    .Include(x => x.ToAsset)
                    .Include(x => x.ToLocation)
                    .Include(x => x.TransportRequestBulkCargoAttachments)
                    .Where(x => x.TransportRequestId == transportRequestId)
                    .OrderBy(s => s.CreatedDate)
                    .ToListAsync();

            }
            catch (Exception ex)
            {
                throw new Exception("Error getting Transport Request Bulk Cargo items", ex);
            }

            try
            {
                trmds = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                    .Query()
                    .AsNoTracking()
                    .Where(x => x.TransportRequestId == transportRequestId)
                    .OrderBy(s => s.CreatedDate)
                    .ToListAsync();

                // Load related TransportRequest entities
                var transportRequestIds = trmds.Select(x => x.TransportRequestId).Distinct().ToList();
                var transportRequests = await _unitOfWork.Repository<TransportRequest>()
                    .Query()
                    .AsNoTracking()
                    .Where(x => transportRequestIds.Contains(x.TransportRequestId))
                    .ToListAsync();

                // Load related TransportRequestMaterialDetailDangerousGood entities
                var trmdDangerousGoodIds = trmds.Select(x => x.TransportRequestMaterialDetailDangerousGoodId).Distinct().ToList();
                var trmdDangerousGoods = await _unitOfWork.Repository<TransportRequestMaterialDetailDangerousGood>()
                    .Query()
                    .AsNoTracking()
                    .Include(x => x.TransportRequestCargoDangerousGood).ThenInclude(x => x.DangerousGood)
                    .Include(x => x.TransportRequestBulkCargoDangerousGood).ThenInclude(x => x.DangerousGood)
                    .Where(x => trmdDangerousGoodIds.Contains(x.TransportRequestMaterialDetailDangerousGoodId))
                    .ToListAsync();

                // Load related TransportRequestCargo entities
                var trcIds = trmds.Select(x => x.TransportRequestCargoId).Distinct().ToList();
                var transportRequestCargos = await _unitOfWork.Repository<TransportRequestCargo>()
                    .Query()
                    .AsNoTracking()
                    .Include(x => x.Cargo).ThenInclude(x => x.Vendor)
                    .Include(x => x.ToAsset)
                    .Include(x => x.ToLocation)
                    .Include(x => x.FromAsset)
                    .Include(x => x.FromLocation)
                    .Include(x => x.TransportRequest).ThenInclude(x => x.Voyage)
                    .Where(x => trcIds.Contains(x.TransportRequestCargoId))
                    .ToListAsync();

                // Load related TransportRequestBulkCargo entities
                var trbcIds = trmds.Select(x => x.TransportRequestBulkCargoId).Distinct().ToList();
                var transportRequestBulkCargos = await _unitOfWork.Repository<TransportRequestBulkCargo>()
                    .Query()
                    .AsNoTracking()
                    .Include(x => x.BulkType)
                    .Include(x => x.ToAsset)
                    .Include(x => x.ToLocation)
                    .Include(x => x.FromAsset)
                    .Include(x => x.FromLocation)
                    .Include(x => x.TransportRequest).ThenInclude(x => x.Voyage)
                    .Where(x => trbcIds.Contains(x.TransportRequestBulkCargoId))
                    .ToListAsync();

                // Load related TransportRequestMaterialDetailAttachments entities
                var trmdAttachmentIds = trmds.Select(x => x.TransportRequestMaterialDetailId).Distinct().ToList();
                var trmdAttachments = await _unitOfWork.Repository<TransportRequestmaterialDetailAttachment>()
                    .Query()
                    .AsNoTracking()
                    .Where(x => trmdAttachmentIds.Contains(x.TransportRequestmaterialDetailId))
                    .ToListAsync();

                // Attach related entities to the main entities
                foreach (var trmd in trmds)
                {
                    trmd.TransportRequest = transportRequests.FirstOrDefault(x => x.TransportRequestId == trmd.TransportRequestId);
                    trmd.TransportRequestMaterialDetailDangerousGood = trmdDangerousGoods.FirstOrDefault(x => x.TransportRequestMaterialDetailDangerousGoodId == trmd.TransportRequestMaterialDetailDangerousGoodId);
                    trmd.TransportRequestCargo = transportRequestCargos.FirstOrDefault(x => x.TransportRequestCargoId == trmd.TransportRequestCargoId);
                    trmd.TransportRequestBulkCargo = transportRequestBulkCargos.FirstOrDefault(x => x.TransportRequestBulkCargoId == trmd.TransportRequestBulkCargoId);
                    trmd.TransportRequestMaterialDetailAttachments = trmdAttachments.Where(x => x.TransportRequestmaterialDetailId == trmd.TransportRequestMaterialDetailId).ToList();
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Error getting Transport Request Material Detail items", ex);
            }
            return (trcs, trbcs, trmds);
        }

        public async Task CreateVoyageCargoSnapshot(
            (string vcs, string vBcs, string vMds) voyageItems,
            Guid? voyageId,
            Guid userId,
            DateTime dateTimeNow
            )
        {
            Guid snapshotId = Guid.NewGuid();
            int version = await _unitOfWork.Repository<VoyageCargoSnapshot>().Query(x => x.VoyageId == voyageId).CountAsync();

            var vCSnapshot = new VoyageCargoSnapshot
            {
                VoyageCargoSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = userId,
                Version = $"V{++version}",
                Content = voyageItems.vcs,
                VoyageId = (Guid)voyageId,
            };

            var vBcSnapshot = new VoyageCargoBulkSnapshot
            {
                VoyageCargoBulkSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = userId,
                Version = $"V{++version}",
                Content = voyageItems.vBcs,
                VoyageId = (Guid)voyageId,
            };

            var vMdSnapshot = new VoyageMaterialDetailSnapshot
            {
                VoyageMaterialDetailSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = userId,
                Version = $"V{++version}",
                Content = voyageItems.vMds,
                VoyageId = (Guid)voyageId,
            };

            try
            {
                await _unitOfWork.Repository<VoyageCargoSnapshot>().CreateAsync(vCSnapshot);
                await _unitOfWork.Repository<VoyageCargoBulkSnapshot>().CreateAsync(vBcSnapshot);
                await _unitOfWork.Repository<VoyageMaterialDetailSnapshot>().CreateAsync(vMdSnapshot);

            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackAsync();
                throw new Exception("Error creating Voyage Cargoes Snapshots", ex);
            }
        }

        public async Task<(
            string updatedVoyageCargoes,
            string updatedVoyageBulkCargoes,
            string updatedVoyageMaterialDetails,
            List<VoyageCargo> vC,
            List<VoyageCargoBulk> vCb,
            List<VoyageMaterialDetail> vMd,
            List<VoyageCargoDangerousGood> vCDg,
            List<VoyageCargoBulkDangerousGood> vBCDg
            )>
            MapVoyageCargoesByTRId
            (
                Guid? voyageId,
                DateTime dateTimeNow,
                (List<TransportRequestCargo> trcs, List<TransportRequestBulkCargo> trbcs, List<TransportRequestMaterialDetail> trmds) trItems,
                Guid currentUserId,
                bool bypassApprovedCargoesCheck
            )
        {
            var latestVoyageCargoSnapshot = await GetLatestVoyageCargoSnapshotCargoesByVoyageId((Guid)voyageId);
            var latestVoyageBulkCargoSnapshot = await GetLatestVoyageBulkCargoSnapshotBulkCargoesByVoyageId((Guid)voyageId);
            var latestVoyageMaterialDetailSnapshot = await GetLatestVoyageMaterialDetailSnapshotMaterialDetailsByVoyageId((Guid)voyageId);

            var cargoesToAddToVoyage = GetViableTransportRequestCargoesToAddToVoyage(trItems.trcs, bypassApprovedCargoesCheck);
            var bulksToAddToVoyage = trItems.trbcs;
            var materialDetailsToAddToVoyage = GetViableTransportRequestMaterialDetails(trItems.trmds, cargoesToAddToVoyage, bulksToAddToVoyage);

            var voyageCargoes = MapTrCargoesToVoyageCargoes(voyageId, cargoesToAddToVoyage, dateTimeNow, latestVoyageCargoSnapshot, currentUserId);
            var voyageCargoBulks = MapTrBulkCargoesToVoyageBulkCargoes(voyageId, bulksToAddToVoyage, dateTimeNow, latestVoyageBulkCargoSnapshot, currentUserId);
            var voyageMaterialDetails = await MapTrMaterialDetailsToVoyageMaterialDetailsAsync(voyageId, voyageCargoes, voyageCargoBulks, materialDetailsToAddToVoyage, cargoesToAddToVoyage, dateTimeNow, latestVoyageMaterialDetailSnapshot, currentUserId);

            var cargoesFromRequest = _mapper.Map<List<VoyageCargoModel>>(voyageCargoes);
            var bulksFromRequest = _mapper.Map<List<VoyageCargoBulkModel>>(voyageCargoBulks);
            var materialDetailsFromRequest = _mapper.Map<List<VoyageMaterialDetailModel>>(voyageMaterialDetails);

            var voyageCargoDangerousGoods = MapTransportRequestCargoDangerousGoodsToVoyageCargoDangerousGoods(voyageCargoes, cargoesToAddToVoyage);
            var voyageBulkCargoDangerousGoods = MapTransportRequestBulkCargoDangerousGoodsToVoyageBulkCargoDangerousGoods(voyageCargoBulks, bulksToAddToVoyage);

            cargoesFromRequest = UpdateCargoesMapping(cargoesFromRequest, latestVoyageCargoSnapshot);
            bulksFromRequest = UpdateCargoBulksMapping(bulksFromRequest, latestVoyageBulkCargoSnapshot);
            materialDetailsFromRequest = UpdateMaterialDetailsMapping(materialDetailsFromRequest, latestVoyageMaterialDetailSnapshot);

            latestVoyageCargoSnapshot.AddRange(cargoesFromRequest);
            latestVoyageBulkCargoSnapshot.AddRange(bulksFromRequest);
            latestVoyageMaterialDetailSnapshot.AddRange(materialDetailsFromRequest);

            //ALSO CREATE THE vCDg in the database
            var mappedTransportRequestCargoesForVoyageCargoSnapshot
                = JsonSerializer.Serialize(_mapper.Map<List<VoyageCargoModel>>(latestVoyageCargoSnapshot));

            var mappedTransportRequestBulkCargoesForVoyageBulkCargoSnapShot
                = JsonSerializer.Serialize(_mapper.Map<List<VoyageCargoBulkModel>>(latestVoyageBulkCargoSnapshot));

            var mappedTransportRequestMaterialDetailsForVoyageMaterialDetailSnapshot
                = JsonSerializer.Serialize(_mapper.Map<List<VoyageMaterialDetailModel>>(latestVoyageMaterialDetailSnapshot));

            return (
                mappedTransportRequestCargoesForVoyageCargoSnapshot,
                mappedTransportRequestBulkCargoesForVoyageBulkCargoSnapShot,
                mappedTransportRequestMaterialDetailsForVoyageMaterialDetailSnapshot,
                voyageCargoes,
                voyageCargoBulks,
                voyageMaterialDetails,
                voyageCargoDangerousGoods,
                voyageBulkCargoDangerousGoods
                );
        }

        private List<TransportRequestCargo> GetViableTransportRequestCargoesToAddToVoyage(List<TransportRequestCargo> transportRequestCargoes, bool bypassApprovedCargoesCheck)
        {
            if (bypassApprovedCargoesCheck) return transportRequestCargoes;

            return transportRequestCargoes.Where(
                    x => x.AutoApprove
                    || x.Status == TransportRequestCargoStatus.Approved
                    || x.Status == TransportRequestCargoStatus.Submitted
            ).ToList();
        }

        private List<TransportRequestMaterialDetail> GetViableTransportRequestMaterialDetails(
            List<TransportRequestMaterialDetail> transportRequestMaterialDetail,
            List<TransportRequestCargo> transportRequestCargoes,
            List<TransportRequestBulkCargo> transportRequestBulkCargoes
            )
        {
            return transportRequestMaterialDetail.Where(x => transportRequestCargoes.Any(y => y.TransportRequestCargoId == x.TransportRequestCargoId)
                || transportRequestBulkCargoes.Any(y => y.TransportRequestBulkCargoId == x.TransportRequestBulkCargoId)).ToList();
        }

        private async Task<List<VoyageMaterialDetailModel>> GetLatestVoyageMaterialDetailSnapshotMaterialDetailsByVoyageId(Guid voyageId)
        {
            var latestVoyageMaterialDetailSnapshotMaterialDetails = new List<VoyageMaterialDetailModel>();

            var latestVoyageMaterialDetailSnapshot = await _unitOfWork.Repository<VoyageMaterialDetailSnapshot>()
                .Query(x => x.VoyageId == voyageId)
                .AsNoTracking()
                .OrderByDescending(x => x.CreatedDate)
                .FirstOrDefaultAsync();

            if (latestVoyageMaterialDetailSnapshot?.Content is not null)
                latestVoyageMaterialDetailSnapshotMaterialDetails = JsonSerializer.Deserialize<List<VoyageMaterialDetailModel>>(latestVoyageMaterialDetailSnapshot.Content);

            return latestVoyageMaterialDetailSnapshotMaterialDetails;
        }

        private async Task<List<VoyageCargoBulkModel>> GetLatestVoyageBulkCargoSnapshotBulkCargoesByVoyageId(Guid voyageId)
        {
            var latestVoyageBulkCargoSnapshotBulkCargoes = new List<VoyageCargoBulkModel>();

            var latestVoyageCargoBulkSnapshot = await _unitOfWork.Repository<VoyageCargoBulkSnapshot>()
                .Query(x => x.VoyageId == voyageId)
                .AsNoTracking()
                .OrderByDescending(x => x.CreatedDate)
                .FirstOrDefaultAsync();

            if (latestVoyageCargoBulkSnapshot?.Content is not null)
                latestVoyageBulkCargoSnapshotBulkCargoes = JsonSerializer.Deserialize<List<VoyageCargoBulkModel>>(latestVoyageCargoBulkSnapshot.Content);

            return latestVoyageBulkCargoSnapshotBulkCargoes;
        }

        private async Task<List<VoyageCargoModel>> GetLatestVoyageCargoSnapshotCargoesByVoyageId(Guid voyageId)
        {
            var latestVoyageCargoSnapshotCargoes = new List<VoyageCargoModel>();

            var latestVoyageCargoSnapshot = await _unitOfWork.Repository<VoyageCargoSnapshot>()
              .Query(x => x.VoyageId == voyageId)
              .AsNoTracking()
              .OrderByDescending(x => x.UpdatedDate ?? x.CreatedDate)
              .FirstOrDefaultAsync();

            if (latestVoyageCargoSnapshot?.Content is not null)
                latestVoyageCargoSnapshotCargoes = JsonSerializer.Deserialize<List<VoyageCargoModel>>(latestVoyageCargoSnapshot.Content);

            return latestVoyageCargoSnapshotCargoes;
        }

        private List<VoyageCargoModel> UpdateCargoesMapping(List<VoyageCargoModel> cargoesFromRequest, List<VoyageCargoModel> latestVcSnapshot)
        {
            cargoesFromRequest.ForEach(requestCargo =>
            {
                requestCargo.RowNumber = latestVcSnapshot.FirstOrDefault(sc =>
                         sc.TransportRequestCargoId == requestCargo.TransportRequestCargoId)?.RowNumber ?? requestCargo.RowNumber;

                requestCargo.VoyageCargoId = latestVcSnapshot.FirstOrDefault(sc =>
                         sc.TransportRequestCargoId == requestCargo.TransportRequestCargoId)?.VoyageCargoId ?? requestCargo.VoyageCargoId;

                latestVcSnapshot.RemoveAll(x => x.TransportRequestCargoId == requestCargo.TransportRequestCargoId);
            });
            return cargoesFromRequest;
        }

        List<VoyageCargoBulkModel> UpdateCargoBulksMapping(List<VoyageCargoBulkModel> bulksFromRequest, List<VoyageCargoBulkModel> latestVBcSnapshot)
        {
            bulksFromRequest.ForEach(requestBulkCargo =>
            {
                requestBulkCargo.RowNumber = latestVBcSnapshot.FirstOrDefault(sc =>
                        sc.TransportRequestBulkCargoId == requestBulkCargo.TransportRequestBulkCargoId)?.RowNumber ?? requestBulkCargo.RowNumber;

                requestBulkCargo.VoyageCargoBulkId = latestVBcSnapshot.FirstOrDefault(sc =>
                         sc.TransportRequestBulkCargoId == requestBulkCargo.TransportRequestBulkCargoId)?.VoyageCargoBulkId ?? requestBulkCargo.VoyageCargoBulkId;

                latestVBcSnapshot.RemoveAll(x => x.TransportRequestBulkCargoId == x.TransportRequestBulkCargoId);
            });
            return bulksFromRequest;
        }

        List<VoyageMaterialDetailModel> UpdateMaterialDetailsMapping(List<VoyageMaterialDetailModel> materialDetailsFromRequest, List<VoyageMaterialDetailModel> latestVMdSnapshot)
        {
            materialDetailsFromRequest.ForEach(requestMaterialDetail =>
            {
                requestMaterialDetail.RowNumber = latestVMdSnapshot.FirstOrDefault(sc =>
                           sc.TransportRequestMaterialDetailId == requestMaterialDetail.TransportRequestMaterialDetailId)?.RowNumber ?? requestMaterialDetail.RowNumber;

                requestMaterialDetail.VoyageMaterialDetailId = latestVMdSnapshot.FirstOrDefault(sc =>
                           sc.TransportRequestMaterialDetailId == requestMaterialDetail.TransportRequestMaterialDetailId)?.VoyageMaterialDetailId ??
                           requestMaterialDetail.VoyageMaterialDetailId;

                latestVMdSnapshot.RemoveAll(x => x.TransportRequestMaterialDetailId == x.TransportRequestMaterialDetailId);
            });

            return materialDetailsFromRequest;
        }

        public async Task<Guid?> CheckTRVoyageRelationship(Guid transportRequestId)
        {
            return await _unitOfWork.Repository<TransportRequest>()
                .Query(x => x.TransportRequestId == transportRequestId && !x.IsComplete && x.Voyage.VoyageStatus != VoyageStatus.Complete)
                .AsNoTracking()
                .Include(x => x.SailingRequest)
                .Select(x => x.VoyageId)
                .FirstOrDefaultAsync();
        }

        public List<VoyageCargo> MapTrCargoesToVoyageCargoes(
            Guid? voyageId,
            List<TransportRequestCargo> cargoesToAddToVoyage,
            DateTime dateTimeNow,
            List<VoyageCargoModel> latestVcSnapshot,
            Guid currentUserId
            )
        {
            int vcRowNumber = latestVcSnapshot.Count;

            return cargoesToAddToVoyage
                .Select(x => new VoyageCargo
                {
                    TransportRequestCargoId = x.TransportRequestCargoId,
                    VoyageId = voyageId,
                    IsCancelled = x.IsCancelled,
                    CreatedById = currentUserId,
                    RowNumber = ++vcRowNumber,
                    CreatedDate = dateTimeNow,
                    Status = VoyageCargoStatus.Submitted,
                    AssetId = x.TransportRequest.VoyageDirection == VoyageDirection.Outbound ? x.ToAssetId : x.FromAssetId,
                    Quantity = x.Quantity,
                    NumberOfLifts = x.NumberOfLifts,
                    CcuId = x.OneOffCcuId is not null ? x.OneOffCcuId : x.Cargo.CCUId,
                    CargoDescription = x.CargoDescription,
                    CargoWeightKg = x.EstimatedCargoWeight,
                    PriorityOrder = x.PriorityOrder,
                    CargoLengthMm = x.CargoLength,
                    CargoWidthMm = x.CargoWidth,
                    CargoId = x.CargoId,
                    TransportRequest = MapTransportRequirementToTransportRequest(x.TransportRequirement, x.TransportRequest.VoyageDirection),
                    RtRob = x.TransportRequirement == TransportRequirement.Transfer ? RtRob.RoundTrip : null,
                    VoyageCargoDangerousGoods = x.TransportRequestCargoDangerousGoods.Select(y => new VoyageCargoDangerousGood
                    {
                        TransportRequestCargoDangerousGoodId = y.TransportRequestCargoDangerousGoodId,
                        DangerousGoodId = y.DangerousGoodId,
                        LtdQty = y.LtdQty ?? false,
                        MarinePollutant = y.MarinePollutant ?? false
                    }).ToList(),
                    Level = 0,
                    CustomStatus = VoyageCargoCustomStatus.FCG,
                    Comments = x.Comments,
                    VendorId = x.VendorId,
                    Vendor = x.Vendor,
                    ViaVendorId = x.ViaVendorId,
                    VendorWarehouseId = x.VendorWarehouseId,
                    ViaVendorWarehouseId = x.ViaVendorWarehouseId,
                    CollectDate = x.CollectDate is not null ? DateOnly.FromDateTime((DateTime)x.CollectDate) : null,
                    CollectTime = x.CollectDate is not null ? TimeOnly.FromDateTime((DateTime)x.CollectDate) : null,
                })
                .ToList();
        }
        public List<VoyageCargoBulk> MapTrBulkCargoesToVoyageBulkCargoes(
            Guid? voyageId,
            List<TransportRequestBulkCargo> bulksToAddToVoyage,
            DateTime dateTimeNow,
            List<VoyageCargoBulkModel> latestVbcSnapshot,
            Guid currentUserId
        )
        {
            int vbcRowNumber = latestVbcSnapshot.Count;

            return bulksToAddToVoyage.Select(x => new VoyageCargoBulk
            {
                TransportRequestBulkCargoId = x.TransportRequestBulkCargoId,
                VoyageId = (Guid)voyageId,
                RowNumber = ++vbcRowNumber,
                CreatedDate = dateTimeNow,
                CreatedById = currentUserId,
                IsCancelled = x.IsCancelled,
                Status = VoyageCargoBulkStatus.Submitted,
                AssetId = (Guid)(x.TransportRequest.VoyageDirection == VoyageDirection.Outbound ? x.ToAssetId : x.FromAssetId),
                UnitName = x.BulkType.UnitName,
                PPGSG = x.Sg,
                BulkTypeId = x.BulkTypeId,
                Quantity = x.Quantity,
                VendorId = x.VendorId,
                Vendor = x.Vendor,
                VoyageCargoBulkDangerousGood = x.TransportRequestBulkCargoDangerousGood is not null ? new VoyageCargoBulkDangerousGood
                {
                    TransportRequestBulkCargoDangerousGoodId = x.TransportRequestBulkCargoDangerousGood.TransportRequestBulkCargoDangerousGoodId,
                    DangerousGoodId = x.TransportRequestBulkCargoDangerousGood.DangerousGoodId,
                    DangerousGood = x.TransportRequestBulkCargoDangerousGood.DangerousGood,
                    LtdQty = (bool)x.TransportRequestBulkCargoDangerousGood.LtdQty,
                    MarinePollutant = (bool)x.TransportRequestBulkCargoDangerousGood.MarinePollutant
                } : null,
            })
            .ToList();
        }

        public async Task<List<VoyageMaterialDetail>> MapTrMaterialDetailsToVoyageMaterialDetailsAsync(
            Guid? voyageId,
            List<VoyageCargo> vC,
            List<VoyageCargoBulk> vCb,
            List<TransportRequestMaterialDetail> materialDetailsToAddToVoyage,
            List<TransportRequestCargo> cargoesToAddToVoyage,
            DateTime dateTimeNow,
            List<VoyageMaterialDetailModel> latestVMdSnapshot,
            Guid currentUserId
)
        {
            var currentVoyageCargoesFromFlow = await _voyageCargoService.GetCargoesByVoyageId((Guid)voyageId);
            var currentVoyageBulkCargoesFromFlow = await _voyageCargoService.GetBulkCargoesByVoyageId((Guid)voyageId);

            int vmdRowNumber = latestVMdSnapshot.Count;

            return materialDetailsToAddToVoyage.Select(x =>
            {
                var cargo = GetCargoFromData(x, vC, currentVoyageCargoesFromFlow);
                var bulkCargo = GetBulkCargoFromData(x, vCb, currentVoyageBulkCargoesFromFlow);
                var dg = GetDangerousGood(x, cargoesToAddToVoyage, vCb);

                return new VoyageMaterialDetail
                {
                    TransportRequestMaterialDetailId = x.TransportRequestMaterialDetailId,
                    RowNumber = ++vmdRowNumber,
                    CreatedById = currentUserId,
                    CreatedDate = dateTimeNow,
                    Status = MaterialDetailStatus.Submitted,
                    VoyageCargoId = cargo?.VoyageCargoId,
                    VoyageCargo = cargo,
                    VoyageCargoBulkId = bulkCargo?.VoyageCargoBulkId,
                    VoyageCargoBulk = bulkCargo,
                    VoyageMaterialDetailDangerousGoodId = dg.bulkCargo?.DangerousGoodId ?? x.TransportRequestMaterialDetailDangerousGoodId ?? null,

                    PackingGroup = dg.cargo?.DangerousGood != null ? (MaterialDetailPackingGroup?)dg.cargo.DangerousGood.PackingGroup
                    : dg.bulkCargo?.DangerousGood != null ? (MaterialDetailPackingGroup?)dg.bulkCargo.DangerousGood.PackingGroup
                    : null,
                    SubClass = dg.cargo?.DangerousGood?.SubClass ?? dg.bulkCargo?.DangerousGood?.SubClass ?? null,
                    MarinePollutant = dg.cargo?.MarinePollutant ?? dg.bulkCargo?.MarinePollutant ?? false,

                    IsCancelled = x.IsCancelled,
                    Quantity = x.Quantity,
                    PackingUnit = x.PackingUnit,
                    MaterialDescription = x.Description,
                    PONo = x.PoNumber,
                    VendorId = x.VendorId,
                    Vendor = x.Vendor,
                    Requester = x.Requestor,
                    WhsLocation = x.PickupLocation,
                    Comments = x.Comments,
                    CustomStatus = x.CustomsStatus is not null ? (MaterialDetailCustomsStatus?)x.CustomsStatus : null,
                    CustomsEntryType = x.CustomsEntryType is not null ? (MaterialDetailCustomsEntryType)x.CustomsEntryType : null,
                    DocumentNo = x.CustomsDocumentNumber,
                    SerialNo = x.SerialNumber,
                    WeightKg = x.EstimatedWeight,
                    ManifestNo = x.ManifestNumber,
                    Value = x.Value,
                    ProperShippingName = x.ProperShippingName,
                    COO = x.CountryOfOrigin,
                    CommodityCode = x.CommodityCode,
                    WorkOrderNumber = x.WorkOrder,
                    VoyageId = (Guid)voyageId,
                };
            }).ToList();
        }

        private VoyageCargo GetCargoFromData(
            TransportRequestMaterialDetail transportRequestMaterial,
            List<VoyageCargo> voyageCargoes,
            List<VoyageCargo> currentVoyageCargoesFromFlow
            )
        {
            var cargo = new VoyageCargo();

            if (transportRequestMaterial.TransportRequestCargoId is not null)
            {
                cargo = currentVoyageCargoesFromFlow.Where(x => x.TransportRequestCargoId == transportRequestMaterial.TransportRequestCargoId).FirstOrDefault() ??
                    voyageCargoes.Where(y => y.TransportRequestCargoId == transportRequestMaterial.TransportRequestCargoId).FirstOrDefault();
            }
            else cargo = null;

            return cargo;
        }

        private VoyageCargoBulk GetBulkCargoFromData(
            TransportRequestMaterialDetail transportRequestMaterial,
            List<VoyageCargoBulk> voyageCargoBulks,
            List<VoyageCargoBulk> currentVoyageBulkCargoesFromFlow
            )
        {
            var bulkCargo = new VoyageCargoBulk();

            if (transportRequestMaterial.TransportRequestBulkCargoId is not null)
            {
                bulkCargo = currentVoyageBulkCargoesFromFlow.Where(x => x.TransportRequestBulkCargoId == transportRequestMaterial.TransportRequestBulkCargoId).FirstOrDefault()
                             ?? voyageCargoBulks.FirstOrDefault(y => y.TransportRequestBulkCargoId == transportRequestMaterial.TransportRequestBulkCargoId);
            }
            else bulkCargo = null;

            return bulkCargo;
        }

        private (TransportRequestCargoDangerousGood cargo, VoyageCargoBulkDangerousGood bulkCargo) GetDangerousGood
            (
                TransportRequestMaterialDetail trMd,
                List<TransportRequestCargo> trCs,
                List<VoyageCargoBulk> vCbs

            )
        {
            var bulkDangerousGood = vCbs.Where(x => x.TransportRequestBulkCargoId == trMd.TransportRequestBulkCargoId).Select(x => x.VoyageCargoBulkDangerousGood).FirstOrDefault();

            var cargoDangerousGood = trCs
                .Where(x => x.TransportRequestCargoDangerousGoods.Any(trdg =>
                    trdg.TransportRequestCargoDangerousGoodId == trMd.TransportRequestMaterialDetailDangerousGood?.TransportRequestCargoDangerousGoodId)
                )
                .SelectMany(trc =>
                    trc.TransportRequestCargoDangerousGoods)
                    .Where(trdg =>
                        trdg.TransportRequestCargoDangerousGoodId == trMd.TransportRequestMaterialDetailDangerousGood.TransportRequestCargoDangerousGoodId)
               .FirstOrDefault();

            return (cargoDangerousGood, bulkDangerousGood);
        }

        public List<VoyageCargoDangerousGood> MapTransportRequestCargoDangerousGoodsToVoyageCargoDangerousGoods
            (
                List<VoyageCargo> vC,
                List<TransportRequestCargo> cargoesToAddToVoyage
            )
        {
            var vCDg = new List<VoyageCargoDangerousGood>();

            foreach (var cargo in cargoesToAddToVoyage)
            {
                if (cargo.TransportRequestCargoDangerousGoods.Any())
                {

                    var voyageCargo = vC.Where(x => x.TransportRequestCargoId == cargo.TransportRequestCargoId).FirstOrDefault();

                    var mappedVoyageCargoesDg = cargo.TransportRequestCargoDangerousGoods.Select(x => new VoyageCargoDangerousGood
                    {
                        LtdQty = x.LtdQty ?? false,
                        MarinePollutant = x.MarinePollutant ?? false,
                        VoyageCargoId = voyageCargo.VoyageCargoId,
                        DangerousGoodId = x.DangerousGoodId,

                    }).ToList();

                    vCDg.AddRange(mappedVoyageCargoesDg);
                }
            }
            return vCDg;
        }

        public List<VoyageCargoBulkDangerousGood> MapTransportRequestBulkCargoDangerousGoodsToVoyageBulkCargoDangerousGoods
            (
                List<VoyageCargoBulk> vBc,
                List<TransportRequestBulkCargo> bulkCargoesToAddToVoyage
            )
        {
            var vBCDg = new List<VoyageCargoBulkDangerousGood>();

            foreach (var bulk in bulkCargoesToAddToVoyage)
            {
                if (bulk.TransportRequestBulkCargoDangerousGood is not null)
                {

                    var voyageBulkCargo = vBc.Where(x => x.TransportRequestBulkCargoId == bulk.TransportRequestBulkCargoId).FirstOrDefault();

                    var mappedVoyageBulkCargoesDg = new VoyageCargoBulkDangerousGood
                    {
                        LtdQty = (bool)bulk.TransportRequestBulkCargoDangerousGood.LtdQty,
                        MarinePollutant = (bool)bulk.TransportRequestBulkCargoDangerousGood.MarinePollutant,
                        DangerousGoodId = bulk.TransportRequestBulkCargoDangerousGood.DangerousGoodId,
                        TransportRequestBulkCargoDangerousGoodId = bulk.TransportRequestBulkCargoDangerousGoodId
                    };
                    vBCDg.AddRange(mappedVoyageBulkCargoesDg);
                }
            }
            return vBCDg;
        }



        public async Task UpdateCargoesInVoyageByTheCargoesFromRequest(
            (
            List<VoyageCargo> vCToAdd,
            List<VoyageCargoBulk> vBcToAdd,
            List<VoyageMaterialDetail> vMdToAdd,
            List<VoyageCargoDangerousGood> vCDgToAdd,
            List<VoyageCargoBulkDangerousGood> vBCDgToAdd
            ) dataForCreateEdit,
            Guid? voyageId,
            DateTime dateTimeNow,
            Guid currentUserId
        )
        {
            //Cargoes
            (
                List<VoyageCargo> currentCreateEditVoyageCargoes,
                List<VoyageCargo> cargoesForUpdate,
                List<VoyageCargo> cargoesToAdd
             ) = await RetrieveInfoOnCargoes(voyageId, dataForCreateEdit.vCToAdd);

            //Bulks
            (
                List<VoyageCargoBulk> currentCreateEditVoyageBulkCargoes,
                List<VoyageCargoBulk> bulksForUpdate,
                List<VoyageCargoBulk> bulksToAdd
             ) = await RetrieveInfoOnBulkCargoes(voyageId, dataForCreateEdit.vBcToAdd);

            //Material Detail
            (
                List<VoyageMaterialDetail> currentCreateEditVoyageMaterialDetails,
                List<VoyageMaterialDetail> materialDetailsForUpdate,
                List<VoyageMaterialDetail> materialDetailsToAdd
             ) = await RetrieveInfoOnMaterialDetails(voyageId, dataForCreateEdit.vMdToAdd);

            var cargoBulksWithoutNullDGtoUpdate = bulksForUpdate.DeepCopy();
            if (cargoesForUpdate.Any())
                await UpdateFlowCargoesWithRequestCargoes(cargoesForUpdate, currentCreateEditVoyageCargoes, dateTimeNow, currentUserId);
            if (bulksForUpdate.Any())
                await UpdateFlowBulkCargoesWithRequestBulkCargoes(bulksForUpdate, currentCreateEditVoyageBulkCargoes, dateTimeNow, currentUserId);
            
            var cargoMaterialDetailsWithoutNullDGtoUpdate = materialDetailsForUpdate.DeepCopy();

            if (materialDetailsForUpdate.Any())
                await UpdateFlowMaterialDetailWithRequestMaterialDetails(materialDetailsForUpdate, currentCreateEditVoyageMaterialDetails, dateTimeNow, currentUserId);

            // #Content: needed to not lose the dangerous goods that lives on the MANUALLY MAPPED cargoes attained from Request To Flow
            //Otherwise its tracked :(
            var cargoesWithoutNullDGtoAdd = cargoesToAdd.DeepCopy();
            var cargoesWithoutNullDGtoUpdate = cargoesForUpdate.DeepCopy();

            var cargoBulksWithoutNullDGtoAdd = bulksToAdd.DeepCopy();
            var cargoMaterialDetailsWithoutNullDGtoAdd = materialDetailsToAdd.DeepCopy();
            
            //Since the DG were mapped manually on the cargo --> Have to set them to null before creating (its a 1 to many relationship thus
            //it doesnt need to live on the parent object
            cargoesToAdd.ForEach(x => x.VoyageCargoDangerousGoods = null);
            cargoesForUpdate.ForEach(x => x.VoyageCargoDangerousGoods = null);

            bulksToAdd.ForEach(x => x.VoyageCargoBulkDangerousGood = null);
            bulksForUpdate.ForEach(x => x.VoyageCargoBulkDangerousGood = null);
            materialDetailsToAdd.ForEach(x => x.VoyageMaterialDetailDangerousGoodId = null);

            await _unitOfWork.Repository<VoyageCargo>().BulkCreateAsync(cargoesToAdd);
            await _unitOfWork.Repository<VoyageCargoBulk>().BulkCreateAsync(bulksToAdd);
            await _unitOfWork.Repository<VoyageMaterialDetail>().BulkCreateAsync(materialDetailsToAdd);

            await _unitOfWork.SaveChangesAsync();

            // #Content: so that this can work correctly
            await SyncCargoDangerousGoods(cargoesWithoutNullDGtoAdd, cargoesWithoutNullDGtoUpdate);
            await SyncCargoBulkDangerousGoods(cargoBulksWithoutNullDGtoAdd, cargoBulksWithoutNullDGtoUpdate);
            await SyncMaterialDetailsWithNewCargoDangerousGoods(cargoMaterialDetailsWithoutNullDGtoAdd, cargoMaterialDetailsWithoutNullDGtoUpdate, currentUserId);

        }

        private async Task SyncMaterialDetailsWithNewCargoDangerousGoods(
            List<VoyageMaterialDetail> cargoMaterialDetailsWithoutNullDGtoAdd,
            List<VoyageMaterialDetail> cargoMaterialDetailsWithoutNullDGtoUpdate,
            Guid currentUserId
            )
        {
            var voyageId = new Guid();
            
            if (cargoMaterialDetailsWithoutNullDGtoUpdate.Count> 0)
                voyageId = cargoMaterialDetailsWithoutNullDGtoUpdate[0].VoyageId;

            if (cargoMaterialDetailsWithoutNullDGtoAdd.Count > 0)
                voyageId = cargoMaterialDetailsWithoutNullDGtoAdd[0].VoyageId;

            var allVoyageMaterialDetails = await _unitOfWork.Repository<VoyageMaterialDetail>()
                    .Query(x => x.VoyageId == voyageId)
                    .ToListAsync();

            //Create
            if (cargoMaterialDetailsWithoutNullDGtoAdd.Count > 0)
            {
                var materialDetailsToAddDg = allVoyageMaterialDetails.Where(x => cargoMaterialDetailsWithoutNullDGtoAdd
                    .Any(y => y.TransportRequestMaterialDetailId == x.TransportRequestMaterialDetailId)).ToList();

                cargoMaterialDetailsWithoutNullDGtoAdd = cargoMaterialDetailsWithoutNullDGtoAdd.Where(x => x.VoyageMaterialDetailDangerousGoodId is not null).ToList();

                foreach (var materialDetail in cargoMaterialDetailsWithoutNullDGtoAdd)
                {
                    if (!materialDetail.VoyageCargoBulkId.HasValue)
                    {
                        var voyageMaterialDetailDangerousGood = await CreateVoyageMaterialDetailDangerousGood(
                            (Guid)materialDetail.VoyageMaterialDetailDangerousGoodId,
                            currentUserId, voyageId
                            );

                        var voyageMaterialDetailsInFlowThatNeedDangerousGood = materialDetailsToAddDg
                            .Where(x => x.TransportRequestMaterialDetailId == materialDetail.TransportRequestMaterialDetailId)
                            .ToList();

                        foreach (var voyageMaterialDetail in voyageMaterialDetailsInFlowThatNeedDangerousGood)
                        {
                            voyageMaterialDetail.VoyageMaterialDetailDangerousGoodId = voyageMaterialDetailDangerousGood.VoyageMaterialDetailDangerousGoodId;
                        }
                    }
                }
                await _unitOfWork.SaveChangesAsync();
            } 
            //Update
            else if (cargoMaterialDetailsWithoutNullDGtoUpdate.Count > 0) 
            {
                var materialDetailsToUpdate = allVoyageMaterialDetails.Where(x => cargoMaterialDetailsWithoutNullDGtoUpdate
                    .Any(y => y.TransportRequestMaterialDetailId == x.TransportRequestMaterialDetailId)).ToList();

                foreach (var mappedVoyageMaterialDetail in cargoMaterialDetailsWithoutNullDGtoUpdate)
                {
                    if (!mappedVoyageMaterialDetail.VoyageCargoBulkId.HasValue)
                    {
                        if (!mappedVoyageMaterialDetail.VoyageMaterialDetailDangerousGoodId.HasValue)
                        {
                            foreach (var materialDetailToUpdate in materialDetailsToUpdate)
                            {
                                materialDetailToUpdate.VoyageMaterialDetailDangerousGoodId = null;
                                materialDetailToUpdate.UpdatedById = currentUserId;
                                materialDetailToUpdate.UpdatedDate = DateTime.UtcNow;
                            }
                        } else
                        {
                            var voyageMaterialDetailDangerousGood = await CreateVoyageMaterialDetailDangerousGood(
                                (Guid)mappedVoyageMaterialDetail.VoyageMaterialDetailDangerousGoodId,
                                currentUserId,
                                voyageId
                                );

                            var voyageMaterialDetailsInFlowThatNeedDangerousGoodUpdated = materialDetailsToUpdate
                                .Where(x => x.TransportRequestMaterialDetailId == mappedVoyageMaterialDetail.TransportRequestMaterialDetailId)
                                .ToList();

                            foreach (var voyageMaterialDetail in voyageMaterialDetailsInFlowThatNeedDangerousGoodUpdated)
                            {
                                voyageMaterialDetail.VoyageMaterialDetailDangerousGoodId = voyageMaterialDetailDangerousGood.VoyageMaterialDetailDangerousGoodId;
                            }
                        }
                    }
                }
                await _unitOfWork.SaveChangesAsync();
            }
        }

        private async Task<VoyageMaterialDetailDangerousGood> CreateVoyageMaterialDetailDangerousGood(Guid transportRequestCargoDangerousGoodId, Guid currentUserId, Guid voyageId)
        {
            var transportRequestMaterialDetailDangerousGood = await _unitOfWork.Repository<TransportRequestMaterialDetailDangerousGood>()
                .GetAsync(transportRequestCargoDangerousGoodId);

            var transportRequestCargoDangerousGood = await _unitOfWork.Repository<TransportRequestCargoDangerousGood>()
                .GetAsync((Guid)transportRequestMaterialDetailDangerousGood.TransportRequestCargoDangerousGoodId);

            var voyageCargodangerousGood = await _unitOfWork.Repository<VoyageCargoDangerousGood>()
               .Query(x => x.TransportRequestCargoDangerousGoodId == transportRequestCargoDangerousGood.TransportRequestCargoDangerousGoodId && x.VoyageCargo.VoyageId == voyageId)
               .Include(x => x.VoyageCargo)
               .FirstOrDefaultAsync();

            var newVoyageMaterialDetailDangerousGood = new VoyageMaterialDetailDangerousGood
            {
                VoyageCargoDangerousGoodId = voyageCargodangerousGood.VoyageCargoDangerousGoodId,
                CreatedById = currentUserId,
                CreatedDate = DateTime.UtcNow,
            };

            var newlyCreatedVoyageMaterialDetailDangerousGood = await _unitOfWork.Repository<VoyageMaterialDetailDangerousGood>().CreateAsync(newVoyageMaterialDetailDangerousGood);
            await _unitOfWork.SaveChangesAsync();
            return newVoyageMaterialDetailDangerousGood;

        }

        private async Task<(List<VoyageCargo> currentCreateEditVoyageCargoes, List<VoyageCargo> cargoesForUpdate, List<VoyageCargo> cargoesToAdd)>
            RetrieveInfoOnCargoes(Guid? voyageId, List<VoyageCargo> cargoesToAdd)
        {
            var currentCreateEditVoyageCargoes = await _unitOfWork.Repository<VoyageCargo>().Query(x => x.VoyageId == voyageId).ToListAsync();

            currentCreateEditVoyageCargoes.RemoveAll(x => x.TransportRequestCargoId == null);

            var cargoesForUpdate = cargoesToAdd
                .Where(x => currentCreateEditVoyageCargoes.Any(y => y.TransportRequestCargoId == x.TransportRequestCargoId))
                .ToList();

            var cargoesForAdding = cargoesToAdd
                .Where(x => !currentCreateEditVoyageCargoes.Any(y => y.TransportRequestCargoId == x.TransportRequestCargoId))
                .ToList();

            return (currentCreateEditVoyageCargoes, cargoesForUpdate, cargoesForAdding);
        }

        private async Task<(List<VoyageCargoBulk> currentCreateEditVoyageCargoes, List<VoyageCargoBulk> cargoesForUpdate, List<VoyageCargoBulk> bulksToAdd)>
            RetrieveInfoOnBulkCargoes(Guid? voyageId, List<VoyageCargoBulk> bulksToAdd)
        {
            var currentCreateEditVoyageBulkCargoes = await _unitOfWork.Repository<VoyageCargoBulk>().Query(x => x.VoyageId == voyageId).ToListAsync();

            currentCreateEditVoyageBulkCargoes.RemoveAll(x => x.TransportRequestBulkCargoId == null);

            var bulksForUpdate = bulksToAdd
                   .Where(x => currentCreateEditVoyageBulkCargoes.Any(y => y.TransportRequestBulkCargoId == x.TransportRequestBulkCargoId))
                   .ToList();

            var bulksForAdding = bulksToAdd
              .Where(x => !currentCreateEditVoyageBulkCargoes.Any(y => y.TransportRequestBulkCargoId == x.TransportRequestBulkCargoId))
              .ToList();

            return (currentCreateEditVoyageBulkCargoes, bulksForUpdate, bulksForAdding);
        }

        private async Task<(
            List<VoyageMaterialDetail> currentCreateEditVoyageMaterialDetails,
            List<VoyageMaterialDetail> materialDetailsForUpdate,
            List<VoyageMaterialDetail> materialDetailsToAdd)> RetrieveInfoOnMaterialDetails(Guid? voyageId, List<VoyageMaterialDetail> materialDetailsToAdd)
        {
            var currentCreateEditVoyageMaterialDetails = await _unitOfWork.Repository<VoyageMaterialDetail>().Query(x => x.VoyageId == voyageId).ToListAsync();

            currentCreateEditVoyageMaterialDetails.RemoveAll(x => x.TransportRequestMaterialDetailId == null);

            var materialDetailsForUpdate = materialDetailsToAdd
                .Where(x => currentCreateEditVoyageMaterialDetails.Any(y => y.TransportRequestMaterialDetailId == x.TransportRequestMaterialDetailId))
                .ToList();

            var materialDetailsForAdding = materialDetailsToAdd
               .Where(x => !currentCreateEditVoyageMaterialDetails.Any(y => y.TransportRequestMaterialDetailId == x.TransportRequestMaterialDetailId))
               .ToList();


            return (currentCreateEditVoyageMaterialDetails, materialDetailsForUpdate, materialDetailsForAdding);
        }

        public async Task UpdateFlowCargoesWithRequestCargoes(
            List<VoyageCargo> requestCargoes,
            List<VoyageCargo> flowCargoesToUpdate,
            DateTime dateTimeNow,
            Guid currentUserId
            )
        {
            var voyageCargoesToUpdate = new List<VoyageCargo>();

            foreach (var cargo in requestCargoes)
            {
                var cargoToUpdate = flowCargoesToUpdate.FirstOrDefault(x => x.TransportRequestCargoId == cargo.TransportRequestCargoId);

                cargo.VoyageCargoId = cargoToUpdate.VoyageCargoId;
                cargoToUpdate = cargo;
                var dangerousCargoes = cargoToUpdate.VoyageCargoDangerousGoods;
                cargoToUpdate.VoyageCargoDangerousGoods = null;
                voyageCargoesToUpdate.Add(cargoToUpdate);
                cargo.VoyageCargoDangerousGoods = dangerousCargoes;
            }
            await _unitOfWork.Repository<VoyageCargo>().BulkUpdate(voyageCargoesToUpdate);
        }

        public async Task UpdateFlowBulkCargoesWithRequestBulkCargoes(
            List<VoyageCargoBulk> requestBulkCargoes,
            List<VoyageCargoBulk> flowCargoBulksToUpdate,
            DateTime dateTimeNow,
            Guid currentUserId
            )
        {
            var voyagecargoeBulksToUpdate = new List<VoyageCargoBulk>();

            foreach (var requestBulkCargo in requestBulkCargoes)
            {
                var bulkToUpdate = flowCargoBulksToUpdate
                .FirstOrDefault(x => x.TransportRequestBulkCargoId == requestBulkCargo.TransportRequestBulkCargoId);

                requestBulkCargo.VoyageCargoBulkId = bulkToUpdate.VoyageCargoBulkId;
                bulkToUpdate = requestBulkCargo;
                bulkToUpdate.VoyageCargoBulkDangerousGood = null;
                voyagecargoeBulksToUpdate.Add(bulkToUpdate);
            }
            await _unitOfWork.Repository<VoyageCargoBulk>().BulkUpdate(voyagecargoeBulksToUpdate);
        }

        public async Task UpdateFlowMaterialDetailWithRequestMaterialDetails(
            List<VoyageMaterialDetail> requestMaterialDetails,
            List<VoyageMaterialDetail> flowMaterialDetailsToUpdate,
            DateTime dateTimeNow,
            Guid currentUserId
            )
        {
            var voyageMaterialDetailsToUpdate = new List<VoyageMaterialDetail>();

            foreach (var requestMaterialDetail in requestMaterialDetails)
            {
                var materialDetailToUpdate = flowMaterialDetailsToUpdate
                    .FirstOrDefault(x => x.TransportRequestMaterialDetailId == requestMaterialDetail.TransportRequestMaterialDetailId);

                requestMaterialDetail.VoyageMaterialDetailId = materialDetailToUpdate.VoyageMaterialDetailId;
                requestMaterialDetail.VoyageMaterialDetailDangerousGoodId = null;

                materialDetailToUpdate = requestMaterialDetail;

                voyageMaterialDetailsToUpdate.Add(materialDetailToUpdate);
            }
            await _unitOfWork.Repository<VoyageMaterialDetail>().BulkUpdate(voyageMaterialDetailsToUpdate);
        }

        private async Task SyncCargoDangerousGoods(List<VoyageCargo> cargoesToUpsert, List<VoyageCargo> cargoesForUpdate)
        {
            cargoesToUpsert.AddRange(cargoesForUpdate);

            List<VoyageCargoDangerousGood> newVoyageDg = new();

            foreach (var cargo in cargoesToUpsert)
            {
                var doestRequestCargoHaveAnyDangerousGoods = cargo.VoyageCargoDangerousGoods != null && cargo.VoyageCargoDangerousGoods.Any();

                if (doestRequestCargoHaveAnyDangerousGoods)
                {
                    var existingDangerousGoods = await _unitOfWork.Repository<VoyageCargoDangerousGood>()
                        .Query(x => x.VoyageCargoId == cargo.VoyageCargoId)
                        .ToListAsync();

                    existingDangerousGoods.RemoveAll(x => x.TransportRequestCargoDangerousGoodId == null);

                    var listToAdd = cargo.VoyageCargoDangerousGoods
                        .Where(x => !existingDangerousGoods.Any(y => y.TransportRequestCargoDangerousGoodId == x.TransportRequestCargoDangerousGoodId))
                        .ToList();

                    var listToUpdate = cargo.VoyageCargoDangerousGoods
                        .Where(x => existingDangerousGoods.Any(y => y.TransportRequestCargoDangerousGoodId == x.TransportRequestCargoDangerousGoodId))
                        .ToList();

                    if (listToAdd.Any())
                    {
                        var newVoyageDgToAdd = cargo.VoyageCargoDangerousGoods.Select(x => new VoyageCargoDangerousGood
                        {
                            TransportRequestCargoDangerousGoodId = x.TransportRequestCargoDangerousGoodId,
                            MarinePollutant = x.MarinePollutant,
                            VoyageCargoId = cargo.VoyageCargoId,
                            DangerousGoodId = x.DangerousGoodId,
                            LtdQty = x.LtdQty,
                        })
                        .ToList();

                        newVoyageDg.AddRange(newVoyageDgToAdd);
                    }

                    await _unitOfWork.Repository<VoyageCargoDangerousGood>().BulkCreateAsync(newVoyageDg);

                    if (listToUpdate.Any())
                    {
                        List<VoyageCargoDangerousGood> dgToBulkUpdate = new();

                        foreach (var dg in listToUpdate)
                        {
                            var dgToAddToBulkUpdate = existingDangerousGoods
                                .FirstOrDefault(x => x.TransportRequestCargoDangerousGoodId == dg.TransportRequestCargoDangerousGoodId);

                            dg.VoyageCargoDangerousGoodId = dgToAddToBulkUpdate.VoyageCargoDangerousGoodId;
                            dg.VoyageCargoId = dgToAddToBulkUpdate.VoyageCargoId;
                            dgToAddToBulkUpdate = dg;
                            dgToBulkUpdate.Add(dgToAddToBulkUpdate);
                        }
                        await DeleteRemovedDangerousGoods(existingDangerousGoods, dgToBulkUpdate);

                        await _unitOfWork.Repository<VoyageCargoDangerousGood>().BulkUpdate(dgToBulkUpdate);
                    }
                }
                else
                {
                    await _unitOfWork.Repository<VoyageCargoDangerousGood>()
                     .DeleteBatchAsync(x => x.VoyageCargoId == cargo.VoyageCargoId);
                }
            }
            await _unitOfWork.SaveChangesAsync();
        }

        private async Task SyncCargoBulkDangerousGoods(List<VoyageCargoBulk> cargoBulksToUpsert, List<VoyageCargoBulk> cargoBulksForUpdate)
        {
            var user = await _userService.GetCurrentUser();
            cargoBulksToUpsert.AddRange(cargoBulksForUpdate);

            var newVoyageBulkCargoDg = new List<VoyageCargoBulkDangerousGood>();

            foreach (var bulk in cargoBulksToUpsert)
            {
                var doestRequestBulkCargoHaveDangerousGood = bulk?.VoyageCargoBulkDangerousGood != null;

                var existingVoyageBulkCargoDangerousGood = await _unitOfWork.Repository<VoyageCargoBulkDangerousGood>()
                    .Query(x => x.VoyageCargoBulkDangerousGoodId == bulk.VoyageCargoBulkDangerousGoodId)
                    .FirstOrDefaultAsync();

                var latestVoyageBulkCargoToUpdatedWithDangerousGood = await _unitOfWork.Repository<VoyageCargoBulk>()
                    .Query(x => x.TransportRequestBulkCargoId == bulk.TransportRequestBulkCargoId)
                    .FirstOrDefaultAsync();

                var latestVoyageMaterialDetailToUpdatedWithDangerousGood = await _unitOfWork.Repository<VoyageMaterialDetail>()
                   .Query(x => x.VoyageCargoBulkId == latestVoyageBulkCargoToUpdatedWithDangerousGood.VoyageCargoBulkId)
                   .ToListAsync();

                if (existingVoyageBulkCargoDangerousGood is null)
                {
                    if (doestRequestBulkCargoHaveDangerousGood)
                    {
                        //create
                        var newVoyageBulkDgToAdd = new VoyageCargoBulkDangerousGood
                        {
                            TransportRequestBulkCargoDangerousGoodId = bulk.VoyageCargoBulkDangerousGood.TransportRequestBulkCargoDangerousGoodId,
                            MarinePollutant = bulk.VoyageCargoBulkDangerousGood.MarinePollutant,
                            DangerousGoodId = bulk.VoyageCargoBulkDangerousGood.DangerousGoodId,
                            LtdQty = bulk.VoyageCargoBulkDangerousGood.LtdQty,
                            CreatedById = user.UserId
                        };

                        await _unitOfWork.Repository<VoyageCargoBulkDangerousGood>().CreateAsync(newVoyageBulkDgToAdd);

                        await _unitOfWork.SaveChangesAsync();

                        latestVoyageBulkCargoToUpdatedWithDangerousGood.VoyageCargoBulkDangerousGoodId = newVoyageBulkDgToAdd.VoyageCargoBulkDangerousGoodId;

                        await _unitOfWork.SaveChangesAsync();

                        var newVoyageMaterialDetailDangerousGood = new VoyageMaterialDetailDangerousGood
                        {
                            VoyageBulkCargoDangerousGoodId = newVoyageBulkDgToAdd.VoyageCargoBulkDangerousGoodId,
                            CreatedById = user.UserId
                        };

                        await _unitOfWork.Repository<VoyageMaterialDetailDangerousGood>().CreateAsync(newVoyageMaterialDetailDangerousGood);

                        await _unitOfWork.SaveChangesAsync();

                        latestVoyageMaterialDetailToUpdatedWithDangerousGood
                            .ForEach(x => x.VoyageMaterialDetailDangerousGoodId = newVoyageMaterialDetailDangerousGood.VoyageMaterialDetailDangerousGoodId);

                        await _unitOfWork.SaveChangesAsync();
                    }
                }
                if (existingVoyageBulkCargoDangerousGood is not null)
                {
                    //update
                    if (!doestRequestBulkCargoHaveDangerousGood)
                    {
                        latestVoyageBulkCargoToUpdatedWithDangerousGood.VoyageCargoBulkDangerousGoodId = null;

                        latestVoyageMaterialDetailToUpdatedWithDangerousGood
                            .ForEach(x => x.VoyageMaterialDetailDangerousGoodId = null);

                        await _unitOfWork.Repository<VoyageCargoDangerousGood>().DeleteAsync(existingVoyageBulkCargoDangerousGood.VoyageCargoBulkDangerousGoodId);
                    }
                    else
                    {
                        existingVoyageBulkCargoDangerousGood.DangerousGoodId = bulk.VoyageCargoBulkDangerousGood.DangerousGoodId;
                        existingVoyageBulkCargoDangerousGood.LtdQty = bulk.VoyageCargoBulkDangerousGood.LtdQty;
                        existingVoyageBulkCargoDangerousGood.MarinePollutant = bulk.VoyageCargoBulkDangerousGood.MarinePollutant;
                        existingVoyageBulkCargoDangerousGood.UpdatedDate = DateTime.UtcNow;
                        existingVoyageBulkCargoDangerousGood.UpdatedById = user.UserId;
                    }
                    _unitOfWork.Repository<VoyageCargoBulkDangerousGood>().Update(existingVoyageBulkCargoDangerousGood);
                }
            }
        }

        private async Task DeleteRemovedDangerousGoods(List<VoyageCargoDangerousGood> existingDangerousGoods, List<VoyageCargoDangerousGood> dgToBulkUpdate)
        {
            var remainingItems = existingDangerousGoods
                            .Where(edg => !dgToBulkUpdate
                                    .Any(dg => dg.TransportRequestCargoDangerousGoodId == edg.TransportRequestCargoDangerousGoodId))
                                .ToList();

            foreach (var remainingItem in remainingItems)
            {
                await _unitOfWork.Repository<VoyageCargoDangerousGood>().DeleteAsync(remainingItem.VoyageCargoDangerousGoodId);
            }

        }

        /// <summary>
        /// Maps TransportRequirement to TransportRequest based on direction and requirement type
        /// </summary>
        /// <param name="transportRequirement">The transport requirement from the request</param>
        /// <param name="direction">The voyage direction (Inbound/Outbound)</param>
        /// <returns>The appropriate VoyageCargoTransportRequest value</returns>
        private VoyageCargoTransportRequest? MapTransportRequirementToTransportRequest(TransportRequirement? transportRequirement, VoyageDirection direction)
        {
            if (!transportRequirement.HasValue)
                return null;

            // Mapping
            // Direction | Request - "Transport Requirement" Value | Flow - "Transport Request" Value
            switch (direction)
            {
                case VoyageDirection.Outbound:
                    switch (transportRequirement.Value)
                    {
                        case TransportRequirement.Interbase:
                        case TransportRequirement.OtherInterbase:
                        case TransportRequirement.Paleiskade:
                            return VoyageCargoTransportRequest.Collection;
                        case TransportRequirement.Vendor:
                            return VoyageCargoTransportRequest.VendorDelivery;
                        case TransportRequirement.Transfer:
                            return VoyageCargoTransportRequest.RoundTrip;
                        default:
                            return null;
                    }
                case VoyageDirection.Inbound:
                    switch (transportRequirement.Value)
                    {
                        case TransportRequirement.Interbase:
                        case TransportRequirement.OtherInterbase:
                        case TransportRequirement.Paleiskade:
                            return VoyageCargoTransportRequest.VendorDelivery;
                        case TransportRequirement.Transfer:
                            return VoyageCargoTransportRequest.RoundTrip;
                        case TransportRequirement.Vendor:
                            return VoyageCargoTransportRequest.Collection;

                        default:
                            return null;
                    }
                default:
                    return null;
            }
        }
    }
}
