import { createActionGroup, emptyProps, props } from '@ngrx/store';
import { HireRequest } from '../../interfaces/hire-request.interface';
import { errorProps } from '../../../../../../../components/src/lib/functions/utility.functions';
import { HireRequestCreate } from '../../interfaces/hire-request-create.interface';
import { HireRequestConfirm } from '../../interfaces/hire-request-confirm.interface';
import { HireRequestEdit } from '../../interfaces/hire-request-edit.interface';
import { HireRequestFilter } from '../../interfaces/hire-request-filter.interface';
import { HireRequestCargo } from '../../interfaces/hire-request-cargo.interface';
import { HireRequestCargoCreate } from '../../interfaces/hire-request-cargo-create.interface';
import { HireRequestCargoConfirmHire } from '../../interfaces/hire-request-cargo-confirm-hire.interface';
import { HireRequestCargoSetOffHiredDate } from '../../interfaces/hire-request-cargo-set-off-hire-date.interface';
import { HireRequestCargoBatchUpdate } from '../../interfaces/hire-request-cargo-batch-update.interface';
import { HireRequestCargoEdit } from '../../interfaces/hire-request-cargo-edit.interface';
import { HireRequestCargoEventUpsert } from '../../interfaces/hire-request-cargo-event-upsert.interface';
import { HireRequestCargoEvent } from '../../interfaces/hire-request-cargo-event.interface';
import { CcuListFilter } from '../../interfaces/ccu-list-filter.interface';

export const HireRequestActions = createActionGroup({
  source: 'HireRequest',
  events: {
    load_Hire_Requests_Waiting_List: props<{
      filterModel: HireRequestFilter;
    }>(),
    load_Hire_Requests_Waiting_List_Success: props<{
      HireRequests: HireRequest[];
    }>(),
    load_Hire_Requests_Waiting_List_Failure: errorProps(),

    load_Hire_Requests_List: props<{ filterModel: HireRequestFilter }>(),
    load_Hire_Requests_List_Success: props<{ HireRequests: HireRequest[] }>(),
    load_Hire_Requests_List_Failure: errorProps(),

    set_Is_Highlighted: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
    }>(),
    set_Is_Highlighted_Success: props<{
      hireRequest: HireRequest;
      filterModel: HireRequestFilter;
      successMessage: string;
    }>(),
    set_Is_Highlighted_Failure: errorProps(),

    set_Awaiting_Collection: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
    }>(),
    set_Awaiting_Collection_Success: props<{
      hireRequest: HireRequest;
      filterModel: HireRequestFilter;
      successMessage: string;
    }>(),
    set_Awaiting_Collection_Failure: errorProps(),

    open_HireRequestDetails_Dialog: props<{
      HireRequest: HireRequest | null;
    }>(),
    close_Hire_Request_Details_Dialog: emptyProps(),

    load_Hire_Request: props<{ id: string }>(),
    load_Hire_Request_Success: props<{ hireRequest: HireRequest }>(),
    load_Hire_Request_Failure: errorProps(),

    create_Hire_Request: props<{
      hireRequest: HireRequestCreate;
      filterModel: HireRequestFilter;
    }>(),
    create_Hire_Request_Success: props<{
      hireRequest: HireRequest;
      filterModel: HireRequestFilter;
      successMessage: string; 
    }>(),
    create_Hire_Request_Failure: errorProps(),

    confirm_Hire_Request: props<{
      hireRequestId: string;
      hireRequest: HireRequestConfirm;
      filterModel: HireRequestFilter;
    }>(),
    confirm_Hire_Request_Success: props<{
      hireRequest: HireRequest;
      filterModel: HireRequestFilter;
      successMessage: string; 
    }>(),
    confirm_Hire_Request_Failure: errorProps(),

    edit_Hire_Request: props<{
      hireRequestId: string;
      hireRequest: HireRequestEdit;
      filterModel: HireRequestFilter;
    }>(),
    edit_Hire_Request_Success: props<{
      hireRequest: HireRequest;
      filterModel: HireRequestFilter;
      successMessage: string; 
    }>(),
    edit_Hire_Request_Failure: errorProps(),

    cancel_Hire_Request: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
    }>(),
    cancel_Hire_Request_Success: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
      successMessage: string; 
    }>(),
    cancel_Hire_Request_Failure: errorProps(),

    delete_Hire_Request: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
    }>(),
    delete_Hire_Request_Success: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
      successMessage: string; 
    }>(),
    delete_Hire_Request_Failure: errorProps(),

    update_Cargo_Hire_Filter: props<{ filter: HireRequestFilter }>(),
    update_Cargo_Hire_Filter_Success: props<{
      filterModel: HireRequestFilter;
    }>(),
    update_Cargo_Hire_Filter_Failure: errorProps(),

    update_Hire_Request_Filter: props<{ filter: HireRequestFilter }>(),
    update_Hire_Request_Filter_Success: props<{
      filterModel: HireRequestFilter;
    }>(),
    update_Hire_Request_Filter_Failure: errorProps(),

    update_Hire_Request_Waiting_List_Filter: props<{
      filter: HireRequestFilter;
    }>(),
    update_Hire_Request_Waiting_List_Filter_Success: props<{
      filterModel: HireRequestFilter;
    }>(),
    update_Hire_Request_Waiting_List_Filter_Failure: errorProps(),

    load_Hire_Request_Cargoes_By_Hire_Request_Id: props<{
      hireRequestId: string;
    }>(),
    load_Hire_Request_Cargoes_By_Hire_Request_Id_Success: props<{
      hireRequestCargoes: HireRequestCargo[];
    }>(),
    load_Hire_Request_Cargoes_By_Hire_Request_Id_Failure: errorProps(),

    remove_Hire_Request_Cargo: props<{
      hireRequestCargoId: string;
      filterModel: HireRequestFilter;
    }>(),
    remove_Hire_Request_Cargo_Success: props<{
      hireRequestId: string;
      filterModel: HireRequestFilter;
      successMessage: string;
    }>(),
    remove_Hire_Request_Cargo_Failure: errorProps(),

    create_Hire_Request_Cargo: props<{
      hireRequestCargo: HireRequestCargoCreate;
    }>(),
    create_Hire_Request_Cargo_Success: props<{
      hireRequestCargo: HireRequestCargo;
      hireRequestId?: string;
      successMessage: string;
    }>(),
    create_Hire_Request_Cargo_Failure: errorProps(),

    create_Cargo_Hire: props<{
      hireRequestCargoId?: string;
      hireRequestCargo: HireRequestCargoConfirmHire;
      filterModel: HireRequestFilter | CcuListFilter;
      hireRequestId?: string;
    }>(),
    create_Cargo_Hire_Success: props<{
      hireRequestCargo: HireRequestCargo;
      filterModel: HireRequestFilter | CcuListFilter;
      hireRequestId?: string;
      successMessage: string;
    }>(),
    create_Cargo_Hire_Failure: errorProps(),

    clear_Filter_Model: emptyProps(),

    load_Cargo_Hires_List: props<{ filterModel: HireRequestFilter }>(),
    load_Cargo_Hires_List_Success: props<{
      HireRequestCargoes: HireRequestCargo[];
    }>(),
    load_Cargo_Hires_List_Failure: errorProps(),

    set_Cargo_Hire_Off_Hired_Date: props<{
      hireRequestCargoId: string;
      hireRequestCargoSetOffHireDateModel: HireRequestCargoSetOffHiredDate;
      filterModel: HireRequestFilter;
    }>(),
    set_Cargo_Hire_Off_Hired_Date_Success: props<{
      hireRequestCargo: HireRequestCargo;
      filterModel: HireRequestFilter;
      successMessage: string;
    }>(),
    set_Cargo_Hire_Off_Hired_Date_Failure: errorProps(),

    create_Cargo_Hire_Note: props<{
      model: HireRequestCargoEventUpsert;
      filterModel: HireRequestFilter;
    }>(),
    create_Cargo_Hire_Note_Success: props<{
      hireRequestCargoEvent: HireRequestCargoEvent;
      hireRequestCargoId: string;
      filterModel: HireRequestFilter;
      successMessage: string;
    }>(),
    create_Cargo_Hire_Note_Failure: errorProps(),

    batch_Update_Cargo_Hires: props<{
      model: HireRequestCargoBatchUpdate;
      filterModel: HireRequestFilter;
    }>(),
    batch_Update_Cargo_Hires_Success: props<{
      hireRequestCargoes: HireRequestCargo[];
      filterModel: HireRequestFilter;
      successMessage: string;
    }>(),
    batch_Update_Cargo_Hires_Failure: errorProps(),

    update_Cargo_Hire: props<{
      hireRequestCargoId: string;
      model: HireRequestCargoEdit;
      filterModel: HireRequestFilter | CcuListFilter;
    }>(),
    update_Cargo_Hire_Success: props<{
      hireRequestCargo: HireRequestCargo;
      filterModel: HireRequestFilter | CcuListFilter;
      hireRequestCargoId: string;
      successMessage: string;
    }>(),
    update_Cargo_Hire_Failure: errorProps(),

    load_Cargo_Hire_Events: props<{ hireRequestCargoId: string }>(),
    load_Cargo_Hire_Events_Success: props<{
      hireRequestCargoEvents: HireRequestCargoEvent[];
    }>(),
    load_Cargo_Hire_Events_Failure: errorProps(),

    remove_Cargo_Hire_Note: props<{
      hireRequestCargoEventId: string;
    }>(),
    remove_Cargo_Hire_Note_Success: props<{
      hireRequestCargoId: string;
      successMessage: string;
    }>(),
    remove_Cargo_Hire_Note_Failure: errorProps(),

    load_Cargo_Hire_By_Id: props<{ hireRequestCargoId: string }>(),
    load_Cargo_Hire_By_Id_Success: props<{
      hireRequestCargo: HireRequestCargo;
      hireRequestCargoId: string;
    }>(),
    load_Cargo_Hire_By_Id_Failure: errorProps(),

    clear_store_hire_request_cargo: emptyProps(),
  },
  
});
