import { Actions, createEffect, ofType } from '@ngrx/effects';
import { inject } from '@angular/core';
import { catchError, filter, map, mergeMap, of, switchMap } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { HireRequestService } from '../../hire-request.service';
import { HireRequestActions } from '../actions/hire-request.actions';
import { HireRequest } from '../../interfaces/hire-request.interface';
import { HireRequestFilter } from '../../interfaces/hire-request-filter.interface';
import { HireRequestStatus } from '../../interfaces/enums/hire-request-status.enum';
import { HireRequestCargo } from '../../interfaces/hire-request-cargo.interface';
import { HireRequestCargoEvent } from '../../interfaces/hire-request-cargo-event.interface';

export const reloadHireRequestWaitingList = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        HireRequestActions.set_Is_Highlighted_Success,
        HireRequestActions.delete_Hire_Request_Success,
        HireRequestActions.create_Hire_Request_Success,
        HireRequestActions.confirm_Hire_Request_Success,
        HireRequestActions.edit_Hire_Request_Success,
        HireRequestActions.cancel_Hire_Request_Success,
        HireRequestActions.update_Hire_Request_Waiting_List_Filter_Success,
        HireRequestActions.create_Cargo_Hire_Success
      ),
      switchMap((action) =>
        of(
          HireRequestActions.load_Hire_Requests_Waiting_List({
            filterModel: action.filterModel,
          })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const reloadHireRequestsList = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        HireRequestActions.set_Awaiting_Collection_Success,
        HireRequestActions.confirm_Hire_Request_Success,
        HireRequestActions.edit_Hire_Request_Success,
        HireRequestActions.cancel_Hire_Request_Success,
        HireRequestActions.update_Hire_Request_Filter_Success,
        HireRequestActions.delete_Hire_Request_Success
      ),
      switchMap((action) =>
        of(
          HireRequestActions.load_Hire_Requests_List({
            filterModel: action.filterModel,
          })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadHireRequestsWaitingList = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.load_Hire_Requests_Waiting_List),
      mergeMap((action) =>
        hireRequestService.loadHireRequests(action.filterModel).pipe(
          map((res: HireRequest[]) =>
            HireRequestActions.load_Hire_Requests_Waiting_List_Success({
              HireRequests: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.load_Hire_Requests_Waiting_List_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadHireRequestsList = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.load_Hire_Requests_List),
      mergeMap((action) =>
        hireRequestService
          .loadHireRequests(action.filterModel, HireRequestStatus.Confirmed)
          .pipe(
            map((res: HireRequest[]) =>
              HireRequestActions.load_Hire_Requests_List_Success({
                HireRequests: res,
              })
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireRequestActions.load_Hire_Requests_List_Failure({
                  error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadCargoHiresList = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.load_Cargo_Hires_List),
      mergeMap((action) =>
        hireRequestService.loadCargoHires(action.filterModel).pipe(
          map((res: HireRequestCargo[]) =>
            HireRequestActions.load_Cargo_Hires_List_Success({
              HireRequestCargoes: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.load_Cargo_Hires_List_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadCargoHireEventsList = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.load_Cargo_Hire_Events),
      mergeMap((action) =>
        hireRequestService.loadCargoHireEvents(action.hireRequestCargoId).pipe(
          map((res: HireRequestCargoEvent[]) =>
            HireRequestActions.load_Cargo_Hire_Events_Success({
              hireRequestCargoEvents: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.load_Cargo_Hire_Events_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadCargoHireById = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.load_Cargo_Hire_By_Id),
      mergeMap((action) =>
        hireRequestService.loadCargoHireById(action.hireRequestCargoId).pipe(
          map((res: HireRequestCargo) =>
            HireRequestActions.load_Cargo_Hire_By_Id_Success({
              hireRequestCargo: res,
              hireRequestCargoId: res.hireRequestCargoId,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.load_Cargo_Hire_By_Id_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const loadHireRequestById = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.load_Hire_Request),
      mergeMap((action) =>
        hireRequestService.loadHireRequest(action.id).pipe(
          map((res: HireRequest) =>
            HireRequestActions.load_Hire_Request_Success({
              hireRequest: res,
            })
          ),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.load_Hire_Request_Failure({
                error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const reloadCargoHireEventsList = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        HireRequestActions.create_Cargo_Hire_Note_Success,
        HireRequestActions.remove_Cargo_Hire_Note_Success,
        HireRequestActions.update_Cargo_Hire_Success,
        HireRequestActions.load_Cargo_Hire_By_Id_Success,
      ),
      switchMap((action) =>
        of(
          HireRequestActions.load_Cargo_Hire_Events({
            hireRequestCargoId: action.hireRequestCargoId,
          })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const reloadCargoHiresList = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(
        HireRequestActions.update_Cargo_Hire_Filter_Success,
        HireRequestActions.set_Cargo_Hire_Off_Hired_Date_Success,
        HireRequestActions.batch_Update_Cargo_Hires_Success,
        HireRequestActions.update_Cargo_Hire_Success,
        HireRequestActions.remove_Hire_Request_Cargo_Success
      ),
      switchMap((action) =>
        of(
          HireRequestActions.load_Cargo_Hires_List({
            filterModel: action.filterModel,
          })
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const setIsHighlighted = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.set_Is_Highlighted),
      mergeMap((action) =>
        hireRequestService.setIsHighlighted(action.hireRequestId).pipe(
          map((res: HireRequest) => {
            return HireRequestActions.set_Is_Highlighted_Success({
              hireRequest: res,
              filterModel: action.filterModel,
              successMessage: `Hire Request ${
                res.isHighlighted ? 'highlighted' : 'unhighlighted'
              } successfully`,
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireRequestActions.set_Is_Highlighted_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const setAwaitingCollection = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.set_Awaiting_Collection),
      mergeMap((action) =>
        hireRequestService.setAwaitingCollection(action.hireRequestId).pipe(
          map((res: HireRequest) => {
            return HireRequestActions.set_Awaiting_Collection_Success({
              hireRequest: res,
              filterModel: action.filterModel,
              successMessage: `Hire Request ${
                res.awaitingCollection
                  ? 'set to Awaiting Collection'
                  : 'removed from Awaiting Collection'
              } `,
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.set_Awaiting_Collection_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const createHireRequest = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.create_Hire_Request),
      mergeMap((action) =>
        hireRequestService.createHireRequest(action.hireRequest).pipe(
          map(() => {
            return HireRequestActions.create_Hire_Request_Success({
              hireRequest: {} as HireRequest,
              filterModel: action.filterModel,
              successMessage: 'Hire Request created successfully',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireRequestActions.create_Hire_Request_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const confirmHireRequest = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.confirm_Hire_Request),
      mergeMap((action) =>
        hireRequestService
          .confirmHireRequest(action.hireRequestId, action.hireRequest)
          .pipe(
            map((res: HireRequest) => {
              return HireRequestActions.confirm_Hire_Request_Success({
                hireRequest: res,
                filterModel: action.filterModel,
                successMessage: 'Hire Request confirmed successfully',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(
                HireRequestActions.confirm_Hire_Request_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const editHireRequest = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.edit_Hire_Request),
      mergeMap((action) =>
        hireRequestService
          .editHireRequest(action.hireRequestId, action.hireRequest)
          .pipe(
            map((res: HireRequest) => {
              return HireRequestActions.edit_Hire_Request_Success({
                hireRequest: res,
                filterModel: action.filterModel,
                successMessage: 'Hire Request edited successfully',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(HireRequestActions.edit_Hire_Request_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const cancelHireRequest = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.cancel_Hire_Request),
      mergeMap((action) =>
        hireRequestService.cancelHireRequest(action.hireRequestId).pipe(
          map(() => {
            return HireRequestActions.cancel_Hire_Request_Success({
              hireRequestId: action.hireRequestId,
              filterModel: action.filterModel,
              successMessage: 'Hire Request cancelled successfully',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireRequestActions.cancel_Hire_Request_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const deleteHireRequest = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.delete_Hire_Request),
      mergeMap((action) =>
        hireRequestService.deleteHireRequest(action.hireRequestId).pipe(
          map(() => {
            return HireRequestActions.delete_Hire_Request_Success({
              hireRequestId: action.hireRequestId,
              filterModel: action.filterModel,
              successMessage: 'Hire Request deleted successfully',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(HireRequestActions.delete_Hire_Request_Failure({ error: error }))
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const updateCargoHireFilter = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(HireRequestActions.update_Cargo_Hire_Filter),
      map((action) =>
        HireRequestActions.update_Cargo_Hire_Filter_Success({
          filterModel: action.filter,
        })
      )
    );
  },
  {
    functional: true,
  }
);

export const updateHireRequestFilter = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(HireRequestActions.update_Hire_Request_Filter),
      map((action) =>
        HireRequestActions.update_Hire_Request_Filter_Success({
          filterModel: action.filter,
        })
      )
    );
  },
  {
    functional: true,
  }
);

export const updateHireRequestWaitingListFilter = createEffect(
  (actions = inject(Actions)) => {
    return actions.pipe(
      ofType(HireRequestActions.update_Hire_Request_Waiting_List_Filter),
      map((action) =>
        HireRequestActions.update_Hire_Request_Waiting_List_Filter_Success({
          filterModel: action.filter,
        })
      )
    );
  },
  {
    functional: true,
  }
);

export const loadHireRequestCargoesByHireRequestId = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(
        HireRequestActions.load_Hire_Request_Cargoes_By_Hire_Request_Id,
        HireRequestActions.remove_Hire_Request_Cargo_Success,
        HireRequestActions.create_Hire_Request_Cargo_Success,
        HireRequestActions.create_Cargo_Hire_Success
      ),
      filter(action => !!action.hireRequestId),
      mergeMap((action) =>
        hireRequestService
          .loadHireRequestCargoesByHireRequestId(action.hireRequestId!)
          .pipe(
            map((response: HireRequestCargo[]) =>
              HireRequestActions.load_Hire_Request_Cargoes_By_Hire_Request_Id_Success(
                {
                  hireRequestCargoes: response,
                }
              )
            ),
            catchError((error: HttpErrorResponse) =>
              of(
                HireRequestActions.load_Hire_Request_Cargoes_By_Hire_Request_Id_Failure(
                  { error: error }
                )
              )
            )
          )
        )
      );
    },
  {
    functional: true,
  }
);

export const removeHireRequestCargo = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.remove_Hire_Request_Cargo),
      mergeMap((action) =>
        hireRequestService
          .deleteHireRequestCargo(action.hireRequestCargoId)
          .pipe(
            map((hireRequestId: string) => {
              return HireRequestActions.remove_Hire_Request_Cargo_Success({
                hireRequestId: hireRequestId,
                filterModel: action.filterModel,
                successMessage: 'Hire Request Cargo deleted successfully',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(
                HireRequestActions.delete_Hire_Request_Failure({ error: error })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const createHireRequestCargo = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.create_Hire_Request_Cargo),
      mergeMap((action) =>
        hireRequestService.createHireRequestCargo(action.hireRequestCargo).pipe(
          map((response: HireRequestCargo) => {
            return HireRequestActions.create_Hire_Request_Cargo_Success({
              hireRequestId: action.hireRequestCargo.hireRequestId,
              hireRequestCargo: response,
              successMessage: 'Hire Request Cargo created successfully',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.create_Hire_Request_Cargo_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const createCargoHire = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.create_Cargo_Hire),
      mergeMap((action) =>
        hireRequestService
          .createCargoHire(action.hireRequestCargo, action.hireRequestCargoId)
          .pipe(
            map((response: HireRequestCargo) => {
              return HireRequestActions.create_Cargo_Hire_Success({
                hireRequestCargo: response,
                filterModel: action.filterModel,
                hireRequestId: action.hireRequestId,
                successMessage: 'Cargo Hire created successfully',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(HireRequestActions.create_Cargo_Hire_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const setCargoHireOffHiredDate = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.set_Cargo_Hire_Off_Hired_Date),
      mergeMap((action) =>
        hireRequestService
          .setCargoHireOffHiredDate(
            action.hireRequestCargoId,
            action.hireRequestCargoSetOffHireDateModel
          )
          .pipe(
            map((res: HireRequestCargo) => {
              return HireRequestActions.set_Cargo_Hire_Off_Hired_Date_Success({
                hireRequestCargo: res,
                filterModel: action.filterModel,
                successMessage: 'Off Hired Date successfully saved',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(
                HireRequestActions.set_Cargo_Hire_Off_Hired_Date_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const createCargoHireNote = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.create_Cargo_Hire_Note),
      mergeMap((action) =>
        hireRequestService.createCargoHireNote(action.model).pipe(
          map((res: HireRequestCargoEvent) => {
            return HireRequestActions.create_Cargo_Hire_Note_Success({
              hireRequestCargoEvent: res,
              hireRequestCargoId: action.model.hireRequestCargoId,
              filterModel: action.filterModel,
              successMessage: 'Cargo Hire Note created successfully',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.create_Cargo_Hire_Note_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const batchUpdateCargoHires = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.batch_Update_Cargo_Hires),
      mergeMap((action) =>
        hireRequestService.batchUpdateCargoHires(action.model).pipe(
          map((res: HireRequestCargo[]) => {
            return HireRequestActions.batch_Update_Cargo_Hires_Success({
              hireRequestCargoes: res,
              filterModel: action.filterModel,
              successMessage: 'CCU Hires updated successfully',
            });
          }),
          catchError((error: HttpErrorResponse) =>
            of(
              HireRequestActions.batch_Update_Cargo_Hires_Failure({
                error: error,
              })
            )
          )
        )
      )
    );
  },
  {
    functional: true,
  }
);

export const updateCargoHire = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.update_Cargo_Hire),
      mergeMap((action) =>
        hireRequestService
          .updateCargoHire(action.hireRequestCargoId, action.model)
          .pipe(
            map((res: HireRequestCargo) => {
              return HireRequestActions.update_Cargo_Hire_Success({
                hireRequestCargo: res,
                filterModel: action.filterModel,
                hireRequestCargoId: action.hireRequestCargoId,
                successMessage: 'Cargo Hire updated successfully',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(HireRequestActions.update_Cargo_Hire_Failure({ error: error }))
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);

export const removeCargoHireNote = createEffect(
  (
    actions = inject(Actions),
    hireRequestService = inject(HireRequestService)
  ) => {
    return actions.pipe(
      ofType(HireRequestActions.remove_Cargo_Hire_Note),
      mergeMap((action) =>
        hireRequestService
          .deleteHireRequestCargoNote(action.hireRequestCargoEventId)
          .pipe(
            map((hireRequestCargoId: string) => {
              return HireRequestActions.remove_Cargo_Hire_Note_Success({
                hireRequestCargoId: hireRequestCargoId,
                successMessage: 'Cargo Hire Note removed successfully',
              });
            }),
            catchError((error: HttpErrorResponse) =>
              of(
                HireRequestActions.remove_Cargo_Hire_Note_Failure({
                  error: error,
                })
              )
            )
          )
      )
    );
  },
  {
    functional: true,
  }
);
