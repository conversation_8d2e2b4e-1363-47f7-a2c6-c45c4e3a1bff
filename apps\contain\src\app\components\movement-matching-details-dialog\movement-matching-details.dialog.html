<p-dialog
  [draggable]="false"
  [closable]="false"
  [modal]="true"
  [(visible)]="dialogVisible"
  [style]="{ width: '1240px' }">
  <ng-template pTemplate="header">
    <span class="p-dialog-title"> Movement Matching {{ movementMatching.exceptionReason ? 'Exception' : 'Match' }}
      Details </span>
  </ng-template>
  <ng-template pTemplate="content">
    <div class="d-flex flex-direction-column h-100">
      <div class="flex-1 d-flex">
        <div class="flex-1 p-20">
          <span class="p-dialog-header f-bold">Hire</span>
          <p-divider></p-divider>
          <div class="p-20">
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Unit</span>
              <span class="field-value">{{ movementMatching.hireUnit }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Client</span>
              <span class="field-value">{{ movementMatching.hireClientName }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Billing Asset</span>
              <span class="flex-1">{{ movementMatching.hireBillingAssetName }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Type</span>
              <span class="flex-1">{{ movementMatching.hireUnitType }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Vendor-Inbound</span>
              <span class="flex-1">{{ movementMatching.hireVendorName }}</span>
            </div>
            <p-divider></p-divider>
            <div class="pt-10">
              <div class="mb-10 d-flex align-items-center">
                <span class="field_label f-bold">On-Hired</span>
                <span class="flex-1">{{ movementMatching.onHiredDate | date:'dd/MM/yyyy'
                  }}</span>
              </div>
              <div class="mb-10 d-flex align-items-center">
                <span class="field_label f-bold">Shipped</span>
                <span class="flex-1">{{ movementMatching.shipped | date:'dd/MM/yyyy' }}</span>
              </div>
              <div class="mb-10 d-flex align-items-center">
                <span class="field_label f-bold">Returned</span>
                <span class="flex-1">{{ movementMatching.returned | date:'dd/MM/yyyy' }}</span>
              </div>
              <div class="mb-10 d-flex align-items-center">
                <span class="field_label f-bold">Off-Hired</span>
                <span class="flex-1">{{ movementMatching.offHiredDate | date:'dd/MM/yyyy' }}</span>
              </div>
            </div>

          </div>
        </div>
        <div class="flex-1 p-20">
          <span class="p-dialog-header f-bold">Movement</span>
          <p-divider></p-divider>
          <div class="p-20">
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Unit</span>
              <span class="field-value">{{ movementMatching.movementUnit }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Client</span>
              <span class="field-value">{{ movementMatching.movementClientName }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Asset</span>
              <span class="field-value">{{ movementMatching.movementAssetName }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Description</span>
              <span class="field-value">{{ movementMatching.cargoDescription }}</span>
            </div>
            <div class="mb-10 d-flex align-items-center">
              <span class="field_label f-bold">Vendor</span>
              <span class="field-value">{{ movementMatching.movementVendorName }}</span>
            </div>
            <p-divider></p-divider>
            <div class="pt-10">
              <div class="mb-10 d-flex align-items-center">
                <span class="field_label f-bold">Direction</span>
                <span class="field-value">{{ movementDirection[movementMatching.direction] }}</span>
              </div>
              <div class="mb-10 d-flex align-items-center">
                <span class="field_label f-bold">Date</span>
                <span class="flex-1">{{ movementMatching.completedDate | date:'dd/MM/yyyy HH:mm' }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="flex-1 p-20 buttons_subsection">
          <div class="mb-10 d-flex align-items-center">
            <button [disabled]="!hireRequestCargo()" class="btn-secondary" type="button" (click)="viewHire()">
              View Hire
            </button>
          </div>
          <div class="mb-10 d-flex align-items-center">
            <button [disabled]="!movementMatching.voyageId" class="btn-secondary" type="button" (click)="viewVoyage()">
              View Voyage
            </button>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
  <ng-template pTemplate="footer">
    <button class="btn-tertiary" (click)="hideDialog()">Cancel</button>
  </ng-template>
</p-dialog>

<contain-cargo-hire-details-dialog
*ngIf="hireDetailsDialogVisible && hireRequestCargo()"
[dialogVisible]="hireDetailsDialogVisible"
(dialogToggle)="viewHire()">
</contain-cargo-hire-details-dialog>