import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  OnInit,
  ViewChild,
  computed,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormGroup,
  FormArray,
  FormControl,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Store } from '@ngrx/store';

import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { Table, TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';

import { MaterialDetailTableFields } from 'apps/flow/src/app/shared/enums';
import { Lookups } from 'apps/flow/src/app/shared/helpers/tables/lookups';
import { MaterialDetail } from 'apps/flow/src/app/shared/interfaces';
import { MaterialDetailActions } from 'apps/flow/src/app/store/actions/material-details.action';
import { bulkListEditFeature } from 'apps/flow/src/app/store/features/bulk-list-edit.feature';
import { materialDetailFeature } from 'apps/flow/src/app/store/features/material-details.feature';
import { voyageCargoBulkValidator } from 'apps/flow/src/app/functions/validators/voyageCargoBulkValidator';

import { FieldType } from 'libs/components/src/lib/enums';
import { HasChangeValue } from 'libs/components/src/lib/pipes/has-change-value.pipe';
import { assetsLocationsFeature } from 'libs/services/src/lib/services/assets-locations/store/features';
import {
  bulkTypesFeature,
  dangerousGoodsFeature,
} from 'libs/services/src/lib/services/maintenance/store/features';
import { VoyageCargoStatus } from 'libs/services/src/lib/services/transport-requests/enums/voyage-cargo-status.enum';
import { CountriesList } from 'libs/services/src/lib/services/shared/countries-list';
import { CurrencyList } from 'libs/services/src/lib/services/shared/currency-list';
import { EqualObjects } from 'libs/components/src/lib/functions/equal-objects';
import { vendorsFeature } from 'libs/services/src/lib/services/maintenance/store/features/vendors.feature';
import { RemoveSecondsPipe } from 'libs/components/src/lib/pipes';
import { removeDuplicates } from 'libs/components/src/lib/functions/utility.functions';
import {
  CellSelectDirective,
  CopyDirective,
  NoneSelectableCellDirective,
  PasteTableValueDirective,
} from 'libs/components/src/lib/directives';
import { cargoListEditPageFeature } from 'libs/services/src/lib/services/voyages/store/features/cargo-list-edit-page.feature';
import { flowFeature } from 'libs/services/src/lib/services/voyages/store/features/flow.feature';
import { DangerousGoodsPopupActions } from 'libs/services/src/lib/services/maintenance/store/actions/dangerous-goods-popup.actions';
import { DangerousGoodsPopupType } from 'libs/services/src/lib/services/maintenance/enums/dangerous-goods-popup-type.enum';

@Component({
  standalone: true,
  selector: 'app-material-details-list-edit-table',
  templateUrl: './material-details-list-edit-table.component.html',
  imports: [
    NgFor,
    NgIf,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    ReactiveFormsModule,
    TableModule,
    InputTextModule,
    InputNumberModule,
    DropdownModule,
    TooltipModule,
    HasChangeValue,
    CheckboxModule,
    CalendarModule,
    NgClass,
    CellSelectDirective,
    CopyDirective,
    PasteTableValueDirective,
    NoneSelectableCellDirective,
  ],
  providers: [DatePipe, RemoveSecondsPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MaterialDetailsListEditTableComponent implements OnInit {
  @ViewChild('table') table!: Table;
  private readonly store = inject(Store);
  private readonly destroyRef = inject(DestroyRef);
  private readonly datePipe = inject(DatePipe);
  private readonly removeSecondsPipe = inject(RemoveSecondsPipe);

  assets = this.store.selectSignal(assetsLocationsFeature.selectAssets);
  vendors = this.store.selectSignal(vendorsFeature.selectLocationVendorsList);
  voyageData = this.store.selectSignal(flowFeature.selectVoyage);
  bulkList = this.store.selectSignal(bulkListEditFeature.selectBulkList);
  bulkTypesList = this.store.selectSignal(bulkTypesFeature.selectBulkTypes);
  cargoList = this.store.selectSignal(cargoListEditPageFeature.selectCargoList);
  dangerousGoods = this.store.selectSignal(
    dangerousGoodsFeature.selectDangerousGoodsByLocationId
  );

  selectedItems = this.store.selectSignal(
    materialDetailFeature.selectSelectedItems
  );
  listColumns = this.store.selectSignal(
    materialDetailFeature.selectMaterialDetailTableListColumns
  );
  filteredMaterialDetailList = this.store.selectSignal(
    materialDetailFeature.filteredMaterialDetailList
  );
  tableWidth = this.store.selectSignal(
    materialDetailFeature.selectMaterialDetailTableWidth
  );
  materialDetailList = signal<any[]>([]);
  tableLoading = this.store.selectSignal(materialDetailFeature.selectIsLoading);

  voyageBulkCargoes = this.store.selectSignal(
    bulkListEditFeature.selectBulkList
  );
  initialTransportRequestMd = this.store.selectSignal(
    materialDetailFeature.selectMaterialDetailList
  );

  dropdownLookups = computed<{
    [key: string]: {
      label: string;
      value: any;
    }[];
  }>(() => {
    return {
      ...Lookups,
      vendorId: this.vendors()?.map((item) => ({
        label: item.vendorName,
        value: item.vendorId,
      })),
      assetId: this.assets()?.map((data) => ({
        label: data.name,
        value: data.assetId,
      })),
      voyageCargoBulkId: removeDuplicates(this.bulkList(), 'bulkTypeName')?.map(
        (data) => ({
          label: data.bulkTypeName,
          value: data.voyageCargoBulkId,
        })
      ),
      voyageCargoId:
        this.cargoList()
          ?.filter((data) => data.ccuId)
          .map((data) => ({
            label: data.ccuId,
            value: data.voyageCargoId,
          })) || [],
      dangerousGoodId: this.dangerousGoods()?.map((data) => ({
        label: data.unNo,
        value: data.dangerousGoodId,
      })),
      coo: CountriesList.map((country) => ({
        label: country.name,
        value: country.code,
      })),
      currency: CurrencyList.map((country) => ({
        label: country.code,
        value: country.code,
      })),
    };
  });

  form = new FormGroup({
    materialDetailList: new FormArray([], {
      validators: voyageCargoBulkValidator(this.bulkList(), this.cargoList()),
    }),
  });
  initialFormValue = this.form.getRawValue();

  fieldType = FieldType;
  materialDetailTableFields = MaterialDetailTableFields;
  status = VoyageCargoStatus;

  get materialDetailListCtrl() {
    return this.form.get('materialDetailList') as FormArray;
  }

  ngOnInit() {
    this.store
      .select(materialDetailFeature.filteredMaterialDetailList)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.initializeFormArray(value);
      });

    this.form.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        if (!EqualObjects(value, this.initialFormValue).isEqual) {
          if (this.selectedItems().length) {
            this.selectedListItems([]);
          }
        }
      });
  }

  initializeFormArray(value: MaterialDetail[]) {
    this.clearFormArray(this.materialDetailListCtrl);
    this.materialDetailList.set([]);
    value.forEach((item) => {
      const formGroup = this.addBulkItem(item);
      if (
        item.voyageMaterialDetailDangerousGood &&
        item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood &&
        item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood.dangerousGood
      ) {
        formGroup.patchValue({
          unNo:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .dangerousGood?.unNo ?? null,
          marinePollutant:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .marinePollutant ?? false,
          ltdQuantity:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .ltdQty ?? false,
          properShippingName:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .dangerousGood?.properShippingName ?? null,
          packingGroup:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .dangerousGood?.packingGroup ?? null,
          class:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .dangerousGood?.class ?? null,
          subClass:
            item.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
              .dangerousGood?.subClass ?? null,
        });
      }
      if (
        item.voyageCargoBulk &&
        item.voyageCargoBulk.voyageCargoBulkDangerousGood &&
        item.voyageCargoBulk.voyageCargoBulkDangerousGood.dangerousGood
      ) {
        formGroup.patchValue({
          unNo:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood.dangerousGood
              .unNo ?? null,
          marinePollutant:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood
              .marinePollutant ?? false,
          ltdQuantity:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood.ltdQty ?? false,
          properShippingName:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood.dangerousGood
              .properShippingName ?? null,
          packingGroup:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood.dangerousGood
              .packingGroup ?? null,
          class:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood.dangerousGood
              .class ?? null,
          subClass:
            item.voyageCargoBulk.voyageCargoBulkDangerousGood.dangerousGood
              .subClass ?? null,
        });
      }

      this.materialDetailListCtrl.push(formGroup, { emitEvent: false });
      this.materialDetailList.update(() => [
        ...this.materialDetailList(),
        item,
      ]);
    });

    this.initialFormValue = this.form.getRawValue();
  }

  save() {
    if (this.materialDetailListCtrl.valid) {
      const materialDetails = [];
      for (let index of this.materialDetailListCtrl.controls.keys()) {
        const materialDetail = {
          ...this.materialDetailListCtrl.at(index!).value,
          collectDate: this.collectDate(
            this.materialDetailListCtrl.at(index!).value.collectDate
          ),
          collectTime: this.collectTime(
            this.materialDetailListCtrl.at(index!).value.collectTime
          ),
          transportRequest:
            this.cargoList().find(
              (x) =>
                x.voyageCargoId ===
                this.materialDetailListCtrl.at(index!).value.voyageCargoId
            )?.transportRequest ?? null,
        };
        materialDetails.push(materialDetail);
      }
      this.store.dispatch(
        MaterialDetailActions.bulk_Update_Voyage_Material_Detail({
          materialDetails,
          voyageId: this.voyageData()?.voyageId!,
        })
      );
    }
  }

  cancel() {
    this.initializeFormArray(this.initialFormValue.materialDetailList);
  }

  clearFormArray(formArray: FormArray) {
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }
  }

  addBulkItem(item: any) {
    return new FormGroup({
      voyageMaterialDetailId: new FormControl(item.voyageMaterialDetailId),
      voyageId: new FormControl(item.voyageId ?? this.voyageData()?.voyageId),
      assetId: new FormControl({
        value: item.assetId,
        disabled: true,
      }),
      rowNumber: new FormControl(item.customStatus),
      unNo: new FormControl({ value: item.unNo, disabled: true }),
      voyageCargoId: new FormControl(item.voyageCargoId, [Validators.required]),
      voyageCargoBulkId: new FormControl(item.voyageCargoBulkId, [
        Validators.required,
      ]),
      status: new FormControl({
        value: item.status ?? VoyageCargoStatus.Draft,
        disabled: true,
      }),
      quantity: new FormControl(item.quantity ?? 0),
      packingUnit: new FormControl(item.packingUnit),
      materialDescription: new FormControl(item.materialDescription),
      whsStatus: new FormControl(item.whsStatus),
      poNo: new FormControl(item.poNo),
      vendorId: new FormControl(item.vendorId),
      requester: new FormControl(item.requester),
      whsLocation: new FormControl(item.whsLocation),
      destinationLocation: new FormControl(item.destinationLocation),
      collectDate: new FormControl(
        item.collectDate ? new Date(item.collectDate) : null
      ),
      collectTime: new FormControl(
        this.removeSecondsPipe.transform(item.collectTime)
      ),
      phone: new FormControl(item.phone),
      transportRequest: new FormControl(item.transportRequest ?? null),
      poTransport: new FormControl(item.poTransport),
      comments: new FormControl(item.comments),
      customStatus: new FormControl(item.customStatus),
      customsEntryType: new FormControl(item.customsEntryType),
      documentNo: new FormControl(item.documentNo),
      serialNo: new FormControl(item.serialNo),
      weight: new FormControl(item.weight ?? 0),
      manifestNo: new FormControl(item.manifestNo),
      value: new FormControl(item.value),
      category: new FormControl(item.category),
      properShippingName: new FormControl(item.properShippingName),
      packingGroup: new FormControl(item.packingGroup),
      imoCode: new FormControl(item.imoCode),
      dangerousGoodId: new FormControl(item.dangerousGoodId),
      class: new FormControl(item.class),
      subClass: new FormControl(item.subClass),
      ltdQuantity: new FormControl(item.ltdQuantity ?? false),
      marinePollutant: new FormControl(item.marinePollutant ?? false),
      coo: new FormControl(item.coo),
      commodityCode: new FormControl(item.commodityCode),
      jobCardNumber: new FormControl(item.jobCardNumber),
      workOrderNumber: new FormControl(item.workOrderNumber),
      mivmmt: new FormControl(item.mivmmt),
      currency: new FormControl(item.currency),
      voyageMaterialDetailDangerousGoodId: new FormControl(
        item.voyageMaterialDetailDangerousGoodId
      ),
      mdBulkDangerousGood: new FormControl(
        item.voyageCargoBulk?.voyageCargoBulkDangerousGood
      ),
      voyageCargoDangerousGoodId: new FormControl(
        item.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGoodId
      ),
    });
  }

  add() {
    this.materialDetailListCtrl.push(this.addBulkItem({} as MaterialDetail));
    this.materialDetailList.update(() => [
      ...this.materialDetailList(),
      { rowIndex: this.materialDetailListCtrl.length - 1 },
    ]);
    setTimeout(() => this.scrollToBottom(), 100); // Ensure DOM updates before scrolling
  }

  selectedListItems(selectedItems: MaterialDetail[]) {
    this.store.dispatch(MaterialDetailActions.select_Items({ selectedItems }));
  }

  changeDropdown(controlName: MaterialDetailTableFields, index: number) {
    const ctrlIndex = this.materialDetailListCtrl.at(index);
    if (controlName === this.materialDetailTableFields.dangerousGoodId) {
      if (ctrlIndex.get(MaterialDetailTableFields.dangerousGoodId)?.value) {
        this.materialDetailListCtrl
          .at(index)
          .get(MaterialDetailTableFields.unNo)
          ?.setValue(
            this.dangerousGoods().filter(
              (item) =>
                item.dangerousGoodId ===
                this.materialDetailListCtrl
                  .at(index)
                  .get(MaterialDetailTableFields.dangerousGoodId)?.value
            )[0].unNo
          );
      } else {
        ctrlIndex.get(MaterialDetailTableFields.unNo)?.setValue(null);
      }
    }

    if (
      controlName === this.materialDetailTableFields.voyageCargoId ||
      controlName === this.materialDetailTableFields.voyageCargoBulkId
    ) {
      const selectedCargoId = ctrlIndex.get(
        MaterialDetailTableFields.voyageCargoId
      )?.value;
      const selectedBulkId = ctrlIndex.get(
        MaterialDetailTableFields.voyageCargoBulkId
      )?.value;

      if (selectedCargoId) {
        const matchedCargo = this.cargoList().find(
          (cargo) => cargo.voyageCargoId === selectedCargoId
        );

        if (matchedCargo) {
          ctrlIndex
            .get(MaterialDetailTableFields.assetId)
            ?.setValue(matchedCargo.assetId);
        } else {
          ctrlIndex.get(MaterialDetailTableFields.assetId)?.setValue(null);
        }
      } else {
        ctrlIndex.get(MaterialDetailTableFields.assetId)?.setValue(null);
      }

      if (selectedBulkId) {
        const matchedCargo = this.bulkList().find(
          (bulkCargo) => bulkCargo.voyageCargoBulkId === selectedBulkId
        );

        if (matchedCargo) {
          ctrlIndex
            .get(MaterialDetailTableFields.assetId)
            ?.setValue(matchedCargo.assetId);
        } else {
          ctrlIndex.get(MaterialDetailTableFields.assetId)?.setValue(null);
        }
      } else {
        ctrlIndex.get(MaterialDetailTableFields.assetId)?.setValue(null);
      }
    }
  }

  collectDate(date: Date) {
    if (!date) {
      return null;
    }
    if (!this.isDate(date)) {
      return date;
    }

    return this.datePipe.transform(date, 'yyyy-MM-dd');
  }

  collectTime(date: Date) {
    if (!date) {
      return null;
    }
    if (!this.isDate(date)) {
      return date;
    }

    return this.datePipe.transform(date, 'HH:mm');
  }

  isDate(value: Date): boolean {
    return value instanceof Date && !isNaN(value.getTime());
  }

  onPaste(data: {
    selectedControlName: string;
    copyRow: number;
    selectedRows: number[];
  }) {
    for (let index of data.selectedRows) {
      this.materialDetailListCtrl
        .at(index)
        .get(data.selectedControlName)
        ?.setValue(
          this.materialDetailListCtrl
            .at(data.copyRow)
            .get(data.selectedControlName)?.value
        );
    }
  }

  private scrollToBottom() {
    const scrollableView = this.table.el.nativeElement.querySelector(
      '.p-datatable-wrapper'
    );
    if (scrollableView) {
      scrollableView.scrollTop = scrollableView.scrollHeight;
    }
  }

  canDeactivate(): boolean {
    const currentValue = { ...this.form.getRawValue() };
    const initialValue = { ...this.initialFormValue };
    return EqualObjects(currentValue, initialValue).isEqual;
  }

  showDangerousGoodsDialog(rowData: any) {
    if (rowData.voyageCargoId) {
      this.openCargoDangerousGoods(rowData);
    } else if (rowData.voyageCargoBulkId) {
      this.openBulkDangerousGood(rowData);
    }
  }

  openCargoDangerousGoods(rowData: any) {
    this.store.dispatch(
      DangerousGoodsPopupActions.change_visibility({
        visible: true,
        cargoId: rowData.voyageCargoId as string,
        popupType: DangerousGoodsPopupType.VoyageCargo,
        materialDetailsId: rowData.voyageMaterialDetailId,
        assignedVoyageCargoDangerousGoodId:
          rowData.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGood
            ?.voyageCargoDangerousGoodId ??
          rowData.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood
            .transportRequestCargoDangerousGoodId ??
          '',
      })
    );
  }

  openBulkDangerousGood(rowData: any) {
    this.store.dispatch(
      DangerousGoodsPopupActions.change_visibility_edit_dialog({
        visible: true,
        dangerousGoods: rowData.voyageCargoBulk?.voyageCargoBulkDangerousGood,
        popupType: DangerousGoodsPopupType.VoyageCargoBulk,
        cargoId: rowData.voyageCargoBulkId as string,
      })
    );
  }
}
